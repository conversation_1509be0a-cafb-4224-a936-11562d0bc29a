import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  Dimensions,
  Animated,
  BackHandler
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../hooks/useTheme';

interface AlertButton {
  text: string;
  onPress?: () => void;
  style?: 'default' | 'cancel' | 'destructive';
}

interface CustomAlertProps {
  visible: boolean;
  title: string;
  message?: string;
  buttons?: AlertButton[];
  onDismiss?: () => void;
  type?: 'info' | 'success' | 'warning' | 'error';
  icon?: string;
}

const { width: screenWidth } = Dimensions.get('window');

const CustomAlert: React.FC<CustomAlertProps> = ({
  visible,
  title,
  message,
  buttons = [{ text: 'OK' }],
  onDismiss,
  type = 'info',
  icon
}) => {
  const { isDark } = useTheme();
  const scaleValue = React.useRef(new Animated.Value(0)).current;
  const opacityValue = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.spring(scaleValue, {
          toValue: 1,
          useNativeDriver: true,
          tension: 100,
          friction: 8,
        }),
        Animated.timing(opacityValue, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();

      const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
        if (onDismiss) {
          onDismiss();
        }
        return true;
      });

      return () => backHandler.remove();
    } else {
      Animated.parallel([
        Animated.spring(scaleValue, {
          toValue: 0,
          useNativeDriver: true,
          tension: 100,
          friction: 8,
        }),
        Animated.timing(opacityValue, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible]);

  const getTypeConfig = () => {
    switch (type) {
      case 'success':
        return {
          iconName: 'checkmark-circle',
          iconColor: '#22c55e',
          backgroundColor: isDark ? '#052e16' : '#f0fdf4',
          borderColor: isDark ? '#15803d' : '#22c55e',
        };
      case 'warning':
        return {
          iconName: 'warning',
          iconColor: '#f59e0b',
          backgroundColor: isDark ? '#451a03' : '#fffbeb',
          borderColor: isDark ? '#d97706' : '#f59e0b',
        };
      case 'error':
        return {
          iconName: 'close-circle',
          iconColor: '#ef4444',
          backgroundColor: isDark ? '#7f1d1d' : '#fef2f2',
          borderColor: isDark ? '#dc2626' : '#ef4444',
        };
      default:
        return {
          iconName: 'information-circle',
          iconColor: '#3b82f6',
          backgroundColor: isDark ? '#1e3a8a' : '#eff6ff',
          borderColor: isDark ? '#2563eb' : '#3b82f6',
        };
    }
  };

  const typeConfig = getTypeConfig();

  const handleButtonPress = (button: AlertButton) => {
    if (button.onPress) {
      button.onPress();
    }
    if (onDismiss) {
      onDismiss();
    }
  };

  const getButtonStyle = (buttonStyle: string) => {
    switch (buttonStyle) {
      case 'destructive':
        return {
          backgroundColor: isDark ? '#7f1d1d' : '#fef2f2',
          borderColor: '#ef4444',
          textColor: '#ef4444',
        };
      case 'cancel':
        return {
          backgroundColor: isDark ? 'transparent' : 'transparent',
          borderColor: isDark ? '#525252' : '#d1d5db',
          textColor: isDark ? '#d1d5db' : '#6b7280',
        };
      default:
        return {
          backgroundColor: '#22c55e',
          borderColor: '#22c55e',
          textColor: '#ffffff',
        };
    }
  };

  if (!visible) return null;

  return (
    <Modal
      transparent
      visible={visible}
      animationType="none"
      onRequestClose={onDismiss}
    >
      <Animated.View 
        className="flex-1 justify-center items-center"
        style={{
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          opacity: opacityValue,
        }}
      >
        <Animated.View
          className={`mx-6 rounded-3xl p-6 shadow-2xl ${
            isDark ? 'bg-neutral-800' : 'bg-white'
          }`}
          style={{
            transform: [{ scale: scaleValue }],
            width: screenWidth - 48,
            maxWidth: 400,
          }}
        >
          {/* Header with Icon */}
          <View className="items-center mb-4">
            <View 
              className="w-16 h-16 rounded-full items-center justify-center mb-3"
              style={{ backgroundColor: typeConfig.backgroundColor }}
            >
              <Ionicons 
                name={icon || typeConfig.iconName as any} 
                size={32} 
                color={typeConfig.iconColor} 
              />
            </View>
            <Text className={`text-xl font-bold text-center ${
              isDark ? 'text-neutral-100' : 'text-neutral-800'
            }`}>
              {title}
            </Text>
          </View>

          {/* Message */}
          {message && (
            <Text className={`text-base text-center mb-6 leading-6 ${
              isDark ? 'text-neutral-300' : 'text-neutral-600'
            }`}>
              {message}
            </Text>
          )}

          {/* Buttons */}
          <View className={`${buttons.length > 1 ? 'flex-row space-x-3' : ''}`}>
            {buttons.map((button, index) => {
              const buttonStyle = getButtonStyle(button.style || 'default');
              return (
                <TouchableOpacity
                  key={index}
                  onPress={() => handleButtonPress(button)}
                  className={`py-3 px-6 rounded-xl border flex-1 ${
                    buttons.length === 1 ? 'items-center' : 'items-center'
                  }`}
                  style={{
                    backgroundColor: buttonStyle.backgroundColor,
                    borderColor: buttonStyle.borderColor,
                  }}
                >
                  <Text 
                    className="text-base font-semibold"
                    style={{ color: buttonStyle.textColor }}
                  >
                    {button.text}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </View>
        </Animated.View>
      </Animated.View>
    </Modal>
  );
};

export default CustomAlert;
