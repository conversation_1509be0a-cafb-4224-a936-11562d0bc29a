import React, { useState } from 'react';
import { 
  View, 
  Text, 
  TextInput, 
  TouchableOpacity, 
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';

import { useAuth } from '../../hooks/useAuth';
import Button from '../../components/Button';

type ResetPasswordRouteProp = RouteProp<{
  ResetPassword: { token: string };
}, 'ResetPassword'>;

const ResetPasswordScreen = () => {
  const navigation = useNavigation();
  const route = useRoute<ResetPasswordRouteProp>();
  const { token } = route.params;
  const { resetPassword } = useAuth();
  
  const [formData, setFormData] = useState({
    password: '',
    confirmPassword: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleResetPassword = async () => {
    // Validation
    if (!formData.password.trim()) {
      Alert.alert('Validation Error', 'Please enter a new password');
      return;
    }

    if (formData.password.length < 6) {
      Alert.alert('Validation Error', 'Password must be at least 6 characters');
      return;
    }

    if (!formData.confirmPassword.trim()) {
      Alert.alert('Validation Error', 'Please confirm your password');
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      Alert.alert('Validation Error', 'Passwords do not match');
      return;
    }

    setIsLoading(true);
    try {
      const result = await resetPassword({
        token,
        password: formData.password,
        confirmPassword: formData.confirmPassword,
      });

      if (result.success) {
        Alert.alert(
          'Password Reset Successful',
          'Your password has been reset successfully. You can now sign in with your new password.',
          [
            {
              text: 'OK',
              onPress: () => {
                navigation.navigate('Login' as never);
              }
            }
          ]
        );
      } else {
        Alert.alert('Reset Failed', result.message);
      }
    } catch (error) {
      Alert.alert('Error', 'Something went wrong. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const getPasswordStrength = (password: string) => {
    let strength = 0;
    if (password.length >= 6) strength++;
    if (password.match(/[a-z]/)) strength++;
    if (password.match(/[A-Z]/)) strength++;
    if (password.match(/[0-9]/)) strength++;
    if (password.match(/[^a-zA-Z0-9]/)) strength++;
    return strength;
  };

  const getPasswordStrengthText = (strength: number) => {
    switch (strength) {
      case 0:
      case 1:
        return { text: 'Weak', color: 'text-red-600' };
      case 2:
      case 3:
        return { text: 'Medium', color: 'text-yellow-600' };
      case 4:
      case 5:
        return { text: 'Strong', color: 'text-green-600' };
      default:
        return { text: 'Weak', color: 'text-red-600' };
    }
  };

  const passwordStrength = getPasswordStrength(formData.password);
  const strengthInfo = getPasswordStrengthText(passwordStrength);

  return (
    <SafeAreaView className="flex-1 bg-white">
      <KeyboardAvoidingView 
        className="flex-1" 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
          <View className="px-6 py-8">
            {/* Header */}
            <View className="items-center mb-8">
              <View className="w-20 h-20 bg-green-500 rounded-2xl items-center justify-center mb-4">
                <Ionicons name="shield-checkmark" size={40} color="#ffffff" />
              </View>
              <Text className="text-3xl font-bold text-neutral-800 mb-2">
                Reset Password
              </Text>
              <Text className="text-base text-neutral-600 text-center">
                Create a new password for your account. Make sure it's strong and secure.
              </Text>
            </View>

            {/* Reset Password Form */}
            <View className="space-y-4">
              {/* New Password Input */}
              <View>
                <Text className="text-base font-semibold text-neutral-800 mb-2">
                  New Password
                </Text>
                <View className="relative">
                  <TextInput
                    className="bg-neutral-100 rounded-lg px-4 py-3 pr-12 text-base text-neutral-800"
                    placeholder="Enter new password"
                    placeholderTextColor="#94a3b8"
                    value={formData.password}
                    onChangeText={(value) => handleInputChange('password', value)}
                    secureTextEntry={!showPassword}
                    autoCapitalize="none"
                    autoCorrect={false}
                    autoFocus
                  />
                  <TouchableOpacity
                    className="absolute right-4 top-3"
                    onPress={() => setShowPassword(!showPassword)}
                  >
                    <Ionicons 
                      name={showPassword ? "eye-off" : "eye"} 
                      size={20} 
                      color="#94a3b8" 
                    />
                  </TouchableOpacity>
                </View>
                
                {/* Password Strength Indicator */}
                {formData.password.length > 0 && (
                  <View className="mt-2">
                    <View className="flex-row items-center justify-between mb-1">
                      <Text className="text-sm text-neutral-600">Password Strength:</Text>
                      <Text className={`text-sm font-semibold ${strengthInfo.color}`}>
                        {strengthInfo.text}
                      </Text>
                    </View>
                    <View className="flex-row space-x-1">
                      {[1, 2, 3, 4, 5].map((level) => (
                        <View
                          key={level}
                          className={`flex-1 h-2 rounded-full ${
                            level <= passwordStrength
                              ? passwordStrength <= 2
                                ? 'bg-red-400'
                                : passwordStrength <= 3
                                ? 'bg-yellow-400'
                                : 'bg-green-400'
                              : 'bg-neutral-200'
                          }`}
                        />
                      ))}
                    </View>
                  </View>
                )}
              </View>

              {/* Confirm Password Input */}
              <View>
                <Text className="text-base font-semibold text-neutral-800 mb-2">
                  Confirm New Password
                </Text>
                <View className="relative">
                  <TextInput
                    className="bg-neutral-100 rounded-lg px-4 py-3 pr-12 text-base text-neutral-800"
                    placeholder="Confirm new password"
                    placeholderTextColor="#94a3b8"
                    value={formData.confirmPassword}
                    onChangeText={(value) => handleInputChange('confirmPassword', value)}
                    secureTextEntry={!showConfirmPassword}
                    autoCapitalize="none"
                    autoCorrect={false}
                  />
                  <TouchableOpacity
                    className="absolute right-4 top-3"
                    onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    <Ionicons 
                      name={showConfirmPassword ? "eye-off" : "eye"} 
                      size={20} 
                      color="#94a3b8" 
                    />
                  </TouchableOpacity>
                </View>
                
                {/* Password Match Indicator */}
                {formData.confirmPassword.length > 0 && (
                  <View className="mt-2 flex-row items-center">
                    <Ionicons 
                      name={formData.password === formData.confirmPassword ? "checkmark-circle" : "close-circle"} 
                      size={16} 
                      color={formData.password === formData.confirmPassword ? "#22c55e" : "#ef4444"} 
                    />
                    <Text className={`text-sm ml-2 ${
                      formData.password === formData.confirmPassword ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {formData.password === formData.confirmPassword ? 'Passwords match' : 'Passwords do not match'}
                    </Text>
                  </View>
                )}
              </View>

              {/* Password Requirements */}
              <View className="bg-blue-50 rounded-2xl p-4 border border-blue-200">
                <Text className="text-sm font-semibold text-blue-800 mb-2">
                  Password Requirements:
                </Text>
                <View className="space-y-1">
                  <View className="flex-row items-center">
                    <Ionicons 
                      name={formData.password.length >= 6 ? "checkmark-circle" : "ellipse-outline"} 
                      size={16} 
                      color={formData.password.length >= 6 ? "#22c55e" : "#94a3b8"} 
                    />
                    <Text className="text-sm text-blue-700 ml-2">At least 6 characters</Text>
                  </View>
                  <View className="flex-row items-center">
                    <Ionicons 
                      name={formData.password.match(/[a-z]/) ? "checkmark-circle" : "ellipse-outline"} 
                      size={16} 
                      color={formData.password.match(/[a-z]/) ? "#22c55e" : "#94a3b8"} 
                    />
                    <Text className="text-sm text-blue-700 ml-2">One lowercase letter</Text>
                  </View>
                  <View className="flex-row items-center">
                    <Ionicons 
                      name={formData.password.match(/[A-Z]/) ? "checkmark-circle" : "ellipse-outline"} 
                      size={16} 
                      color={formData.password.match(/[A-Z]/) ? "#22c55e" : "#94a3b8"} 
                    />
                    <Text className="text-sm text-blue-700 ml-2">One uppercase letter</Text>
                  </View>
                  <View className="flex-row items-center">
                    <Ionicons 
                      name={formData.password.match(/[0-9]/) ? "checkmark-circle" : "ellipse-outline"} 
                      size={16} 
                      color={formData.password.match(/[0-9]/) ? "#22c55e" : "#94a3b8"} 
                    />
                    <Text className="text-sm text-blue-700 ml-2">One number</Text>
                  </View>
                </View>
              </View>

              {/* Reset Password Button */}
              <View className="mt-6">
                <Button
                  title="Reset Password"
                  onPress={handleResetPassword}
                  loading={isLoading}
                  size="lg"
                  fullWidth
                />
              </View>

              {/* Back to Login */}
              <View className="flex-row justify-center items-center mt-6">
                <Text className="text-neutral-600">
                  Remember your password? 
                </Text>
                <TouchableOpacity onPress={() => navigation.navigate('Login' as never)} className="ml-1">
                  <Text className="text-primary-600 font-semibold">
                    Sign In
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default ResetPasswordScreen;
