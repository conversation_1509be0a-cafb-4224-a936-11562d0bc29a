import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  Alert
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { useQuery } from '@tanstack/react-query';
import { Ionicons } from '@expo/vector-icons';

import { api } from '../services/api';
import { Product } from '../types';
import { useCart } from '../hooks/useCart';
import { debounce } from '../utils';

import SearchBar from '../components/SearchBar';
import ProductCard from '../components/ProductCard';
import LoadingSpinner from '../components/LoadingSpinner';

const SearchScreen = () => {
  const navigation = useNavigation();
  const { addItem } = useCart();
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedQuery, setDebouncedQuery] = useState('');

  // Debounce search query
  const debouncedSearch = debounce((query: string) => {
    setDebouncedQuery(query);
  }, 500);

  useEffect(() => {
    debouncedSearch(searchQuery);
  }, [searchQuery]);

  // Search products
  const {
    data: searchResults,
    isLoading,
    error
  } = useQuery({
    queryKey: ['search', debouncedQuery],
    queryFn: () => api.searchProducts(debouncedQuery),
    enabled: debouncedQuery.length > 0,
  });

  const handleProductPress = (product: Product) => {
    navigation.navigate('ProductDetails' as never, {
      productId: product.id
    } as never);
  };

  const handleAddToCart = (product: Product) => {
    addItem(product);
    Alert.alert(
      'Added to Cart',
      `${product.name} has been added to your cart.`,
      [{ text: 'OK' }]
    );
  };

  const renderProductItem = ({ item, index }: { item: Product; index: number }) => (
    <View className={`w-1/2 ${index % 2 === 0 ? 'pr-2' : 'pl-2'} mb-4`}>
      <ProductCard
        product={item}
        onPress={handleProductPress}
        onAddToCart={handleAddToCart}
      />
    </View>
  );

  const renderEmptyState = () => {
    if (searchQuery.length === 0) {
      return (
        <View className="flex-1 items-center justify-center px-6">
          <Ionicons name="search" size={80} color="#cbd5e1" />
          <Text className="text-2xl font-bold text-neutral-800 mt-6 mb-2">
            Search Products
          </Text>
          <Text className="text-base text-neutral-600 text-center">
            Find your favorite groceries and essentials by typing in the search bar above.
          </Text>
        </View>
      );
    }

    if (debouncedQuery.length > 0 && !isLoading && (!searchResults?.data || searchResults.data.length === 0)) {
      return (
        <View className="flex-1 items-center justify-center px-6">
          <Ionicons name="search" size={80} color="#cbd5e1" />
          <Text className="text-2xl font-bold text-neutral-800 mt-6 mb-2">
            No Results Found
          </Text>
          <Text className="text-base text-neutral-600 text-center">
            We couldn't find any products matching "{debouncedQuery}". Try searching with different keywords.
          </Text>
        </View>
      );
    }

    return null;
  };

  const renderRecentSearches = () => {
    const recentSearches = ['Organic Bananas', 'Fresh Milk', 'Bread', 'Chicken'];

    return (
      <View className="px-4 py-6">
        <Text className="text-lg font-bold text-neutral-800 mb-4">
          Recent Searches
        </Text>
        <View className="flex-row flex-wrap">
          {recentSearches.map((search, index) => (
            <TouchableOpacity
              key={index}
              className="bg-neutral-100 rounded-full px-4 py-2 mr-2 mb-2"
              onPress={() => setSearchQuery(search)}
            >
              <Text className="text-sm text-neutral-700">{search}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };

  const renderPopularCategories = () => {
    const categories = [
      { name: 'Fresh Produce', icon: '🥬' },
      { name: 'Dairy & Eggs', icon: '🥛' },
      { name: 'Meat & Seafood', icon: '🥩' },
      { name: 'Bakery', icon: '🍞' },
    ];

    return (
      <View className="px-4 py-6">
        <Text className="text-lg font-bold text-neutral-800 mb-4">
          Popular Categories
        </Text>
        <View className="flex-row flex-wrap">
          {categories.map((category, index) => (
            <TouchableOpacity
              key={index}
              className="bg-white rounded-2xl p-4 shadow-sm border border-neutral-100 mr-3 mb-3 items-center"
              style={{ width: '45%' }}
              onPress={() => setSearchQuery(category.name)}
            >
              <Text className="text-2xl mb-2">{category.icon}</Text>
              <Text className="text-sm font-semibold text-neutral-800 text-center">
                {category.name}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView className="flex-1 bg-neutral-50">
      {/* Header */}
      <View className="bg-white px-4 py-4 shadow-sm">
        <SearchBar
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholder="Search for products..."
        />
      </View>

      {/* Content */}
      <View className="flex-1">
        {searchQuery.length === 0 ? (
          <FlatList
            data={[]}
            renderItem={() => null}
            ListHeaderComponent={
              <View>
                {renderRecentSearches()}
                {renderPopularCategories()}
              </View>
            }
            showsVerticalScrollIndicator={false}
          />
        ) : isLoading ? (
          <LoadingSpinner message="Searching products..." fullScreen />
        ) : error ? (
          <View className="flex-1 items-center justify-center px-6">
            <Ionicons name="alert-circle" size={80} color="#ef4444" />
            <Text className="text-2xl font-bold text-neutral-800 mt-6 mb-2">
              Search Error
            </Text>
            <Text className="text-base text-neutral-600 text-center">
              Something went wrong while searching. Please try again.
            </Text>
          </View>
        ) : (
          <FlatList
            data={searchResults?.data || []}
            renderItem={renderProductItem}
            keyExtractor={(item) => item.id}
            numColumns={2}
            ListHeaderComponent={
              <View className="px-4 pt-4 pb-2">
                <Text className="text-base text-neutral-600">
                  {searchResults?.data?.length || 0} results for "{debouncedQuery}"
                </Text>
              </View>
            }
            ListEmptyComponent={renderEmptyState}
            contentContainerStyle={{ paddingHorizontal: 16, paddingBottom: 20 }}
            showsVerticalScrollIndicator={false}
          />
        )}
      </View>
    </SafeAreaView>
  );
};

export default SearchScreen;
