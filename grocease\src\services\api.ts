import { 
  Product, 
  Category, 
  Banner, 
  User, 
  Order, 
  CartItem, 
  ApiResponse, 
  PaginatedResponse,
  Address 
} from '../types';
import { 
  mockCategories, 
  mockProducts, 
  mockBanners, 
  mockUser 
} from './mockData';

// Simulate network delay
const delay = (ms: number = 1000) => new Promise(resolve => setTimeout(resolve, ms));

// Mock API functions
export const api = {
  // Categories
  getCategories: async (): Promise<ApiResponse<Category[]>> => {
    await delay(800);
    return {
      data: mockCategories,
      success: true,
      message: 'Categories fetched successfully'
    };
  },

  // Products
  getProducts: async (
    categoryId?: string,
    page: number = 1,
    limit: number = 10,
    search?: string
  ): Promise<PaginatedResponse<Product>> => {
    await delay(1200);
    
    let filteredProducts = [...mockProducts];
    
    if (categoryId) {
      filteredProducts = filteredProducts.filter(p => p.category === categoryId);
    }
    
    if (search) {
      const searchLower = search.toLowerCase();
      filteredProducts = filteredProducts.filter(p => 
        p.name.toLowerCase().includes(searchLower) ||
        p.description.toLowerCase().includes(searchLower) ||
        p.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }
    
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedProducts = filteredProducts.slice(startIndex, endIndex);
    
    return {
      data: paginatedProducts,
      pagination: {
        page,
        limit,
        total: filteredProducts.length,
        totalPages: Math.ceil(filteredProducts.length / limit)
      }
    };
  },

  getProduct: async (id: string): Promise<ApiResponse<Product | null>> => {
    await delay(600);
    const product = mockProducts.find(p => p.id === id);
    return {
      data: product || null,
      success: !!product,
      message: product ? 'Product fetched successfully' : 'Product not found'
    };
  },

  // Banners
  getBanners: async (): Promise<ApiResponse<Banner[]>> => {
    await delay(500);
    return {
      data: mockBanners,
      success: true,
      message: 'Banners fetched successfully'
    };
  },

  // User
  getUser: async (): Promise<ApiResponse<User>> => {
    await delay(400);
    return {
      data: mockUser,
      success: true,
      message: 'User data fetched successfully'
    };
  },

  updateUser: async (userData: Partial<User>): Promise<ApiResponse<User>> => {
    await delay(800);
    const updatedUser = { ...mockUser, ...userData };
    return {
      data: updatedUser,
      success: true,
      message: 'User updated successfully'
    };
  },

  // Orders
  createOrder: async (orderData: {
    items: CartItem[];
    deliveryAddress: Address;
    total: number;
    subtotal: number;
    deliveryFee: number;
    discount: number;
  }): Promise<ApiResponse<Order>> => {
    await delay(1500);
    
    const order: Order = {
      id: `order_${Date.now()}`,
      userId: mockUser.id,
      items: orderData.items,
      total: orderData.total,
      subtotal: orderData.subtotal,
      deliveryFee: orderData.deliveryFee,
      discount: orderData.discount,
      status: 'confirmed',
      deliveryAddress: orderData.deliveryAddress,
      orderDate: new Date().toISOString(),
      estimatedDelivery: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString() // 2 hours from now
    };
    
    return {
      data: order,
      success: true,
      message: 'Order placed successfully'
    };
  },

  getOrders: async (userId: string): Promise<ApiResponse<Order[]>> => {
    await delay(1000);

    // Mock orders with realistic data
    const mockOrders: Order[] = [
      {
        id: 'order_1704067200000',
        userId: userId,
        items: [
          { product: mockProducts[0], quantity: 2 },
          { product: mockProducts[1], quantity: 1 },
          { product: mockProducts[3], quantity: 1 },
        ],
        total: 15.27,
        subtotal: 13.27,
        deliveryFee: 2.00,
        discount: 0,
        status: 'delivered',
        deliveryAddress: mockUser.addresses[0],
        orderDate: '2024-01-01T10:30:00Z',
        estimatedDelivery: '2024-01-01T12:30:00Z'
      },
      {
        id: 'order_1704153600000',
        userId: userId,
        items: [
          { product: mockProducts[2], quantity: 1 },
          { product: mockProducts[4], quantity: 2 },
          { product: mockProducts[5], quantity: 1 },
        ],
        total: 24.47,
        subtotal: 24.47,
        deliveryFee: 0,
        discount: 0,
        status: 'out_for_delivery',
        deliveryAddress: mockUser.addresses[0],
        orderDate: '2024-01-02T14:15:00Z',
        estimatedDelivery: '2024-01-02T16:15:00Z'
      },
      {
        id: 'order_1704240000000',
        userId: userId,
        items: [
          { product: mockProducts[1], quantity: 2 },
          { product: mockProducts[3], quantity: 2 },
        ],
        total: 16.56,
        subtotal: 14.56,
        deliveryFee: 2.00,
        discount: 0,
        status: 'preparing',
        deliveryAddress: mockUser.addresses[0],
        orderDate: '2024-01-03T09:45:00Z',
        estimatedDelivery: '2024-01-03T11:45:00Z'
      }
    ];

    return {
      data: mockOrders,
      success: true,
      message: 'Orders fetched successfully'
    };
  },

  getOrderDetails: async (orderId: string): Promise<ApiResponse<Order | null>> => {
    await delay(800);

    // This would normally fetch from the orders list or database
    const ordersResponse = await api.getOrders('1'); // Mock user ID
    const order = ordersResponse.data.find(o => o.id === orderId);

    return {
      data: order || null,
      success: !!order,
      message: order ? 'Order details fetched successfully' : 'Order not found'
    };
  },

  // Search
  searchProducts: async (query: string): Promise<ApiResponse<Product[]>> => {
    await delay(800);
    const searchLower = query.toLowerCase();
    const results = mockProducts.filter(p =>
      p.name.toLowerCase().includes(searchLower) ||
      p.description.toLowerCase().includes(searchLower) ||
      p.tags.some(tag => tag.toLowerCase().includes(searchLower))
    );

    return {
      data: results,
      success: true,
      message: `Found ${results.length} products`
    };
  },

  // Popular Products
  getPopularProducts: async (limit: number = 6): Promise<ApiResponse<Product[]>> => {
    await delay(600);
    // Sort by rating and review count to get popular products
    const popularProducts = [...mockProducts]
      .sort((a, b) => {
        const scoreA = a.rating * Math.log(a.reviewCount + 1);
        const scoreB = b.rating * Math.log(b.reviewCount + 1);
        return scoreB - scoreA;
      })
      .slice(0, limit);

    return {
      data: popularProducts,
      success: true,
      message: 'Popular products fetched successfully'
    };
  },

  // New Products (recently added)
  getNewProducts: async (limit: number = 6): Promise<ApiResponse<Product[]>> => {
    await delay(600);
    // For mock data, we'll just return a subset of products
    // In real app, this would filter by creation date
    const newProducts = [...mockProducts]
      .reverse() // Simulate newest first
      .slice(0, limit);

    return {
      data: newProducts,
      success: true,
      message: 'New products fetched successfully'
    };
  },

  // Featured/Discounted Products
  getFeaturedProducts: async (limit: number = 6): Promise<ApiResponse<Product[]>> => {
    await delay(600);
    // Get products with discounts
    const featuredProducts = mockProducts
      .filter(p => p.discount && p.discount > 0)
      .sort((a, b) => (b.discount || 0) - (a.discount || 0))
      .slice(0, limit);

    return {
      data: featuredProducts,
      success: true,
      message: 'Featured products fetched successfully'
    };
  }
};
