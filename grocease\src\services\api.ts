import {
  Product,
  Category,
  Banner,
  User,
  Order,
  CartItem,
  ApiResponse,
  PaginatedResponse,
  Address,
  CreateOrderRequest,
  CreateAddressRequest,
  UpdateUserRequest
} from '../types';
import { httpClient } from './httpClient';

// API functions
export const api = {
  // Categories
  getCategories: async (): Promise<ApiResponse<Category[]>> => {
    try {
      const response = await httpClient.get<ApiResponse<Category[]>>('/categories');
      return response;
    } catch (error) {
      return {
        data: [],
        success: false,
        message: error instanceof Error ? error.message : 'Failed to fetch categories'
      };
    }
  },

  // Products
  getProducts: async (
    categoryId?: string,
    page: number = 0,
    limit: number = 10,
    search?: string,
    sortBy: string = 'name',
    sortDir: string = 'asc'
  ): Promise<PaginatedResponse<Product>> => {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        sortBy,
        sortDir
      });

      if (categoryId) {
        params.append('categoryId', categoryId);
      }

      if (search) {
        params.append('search', search);
      }

      const response = await httpClient.get<PaginatedResponse<Product>>(`/products?${params}`);
      return response;
    } catch (error) {
      return {
        data: [],
        pagination: {
          page,
          limit,
          total: 0,
          totalPages: 0
        }
      };
    }
  },

  getProduct: async (id: string): Promise<ApiResponse<Product | null>> => {
    try {
      const response = await httpClient.get<ApiResponse<Product>>(`/products/${id}`);
      return response;
    } catch (error) {
      return {
        data: null,
        success: false,
        message: error instanceof Error ? error.message : 'Product not found'
      };
    }
  },

  // Banners
  getBanners: async (): Promise<ApiResponse<Banner[]>> => {
    try {
      const response = await httpClient.get<ApiResponse<Banner[]>>('/banners');
      return response;
    } catch (error) {
      return {
        data: [],
        success: false,
        message: error instanceof Error ? error.message : 'Failed to fetch banners'
      };
    }
  },

  // User
  getUser: async (): Promise<ApiResponse<User>> => {
    try {
      const response = await httpClient.get<ApiResponse<User>>('/users/profile');
      return response;
    } catch (error) {
      return {
        data: null as any,
        success: false,
        message: error instanceof Error ? error.message : 'Failed to fetch user data'
      };
    }
  },

  updateUser: async (userData: UpdateUserRequest): Promise<ApiResponse<User>> => {
    try {
      const response = await httpClient.put<ApiResponse<User>>('/users/profile', userData);
      return response;
    } catch (error) {
      return {
        data: null as any,
        success: false,
        message: error instanceof Error ? error.message : 'Failed to update user'
      };
    }
  },

  // Orders
  createOrder: async (orderData: CreateOrderRequest): Promise<ApiResponse<Order>> => {
    try {
      const response = await httpClient.post<ApiResponse<Order>>('/orders', orderData);
      return response;
    } catch (error) {
      return {
        data: null as any,
        success: false,
        message: error instanceof Error ? error.message : 'Failed to create order'
      };
    }
  },

  getOrders: async (page: number = 0, limit: number = 10): Promise<PaginatedResponse<Order>> => {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString()
      });

      const response = await httpClient.get<PaginatedResponse<Order>>(`/orders?${params}`);
      return response;
    } catch (error) {
      return {
        data: [],
        pagination: {
          page,
          limit,
          total: 0,
          totalPages: 0
        }
      };
    }
  },

  getOrderDetails: async (orderId: string): Promise<ApiResponse<Order | null>> => {
    try {
      const response = await httpClient.get<ApiResponse<Order>>(`/orders/${orderId}`);
      return response;
    } catch (error) {
      return {
        data: null,
        success: false,
        message: error instanceof Error ? error.message : 'Order not found'
      };
    }
  },

  // Search
  searchProducts: async (query: string): Promise<ApiResponse<Product[]>> => {
    try {
      const params = new URLSearchParams({ search: query });
      const response = await httpClient.get<PaginatedResponse<Product>>(`/products?${params}`);
      return {
        data: response.data,
        success: true,
        message: `Found ${response.data.length} products`
      };
    } catch (error) {
      return {
        data: [],
        success: false,
        message: error instanceof Error ? error.message : 'Search failed'
      };
    }
  },

  // Featured Products
  getFeaturedProducts: async (): Promise<ApiResponse<Product[]>> => {
    try {
      const response = await httpClient.get<ApiResponse<Product[]>>('/products/featured');
      return response;
    } catch (error) {
      return {
        data: [],
        success: false,
        message: error instanceof Error ? error.message : 'Failed to fetch featured products'
      };
    }
  },

  // Address Management
  getUserAddresses: async (): Promise<ApiResponse<Address[]>> => {
    try {
      const response = await httpClient.get<ApiResponse<Address[]>>('/users/addresses');
      return response;
    } catch (error) {
      return {
        data: [],
        success: false,
        message: error instanceof Error ? error.message : 'Failed to fetch addresses'
      };
    }
  },

  createAddress: async (addressData: CreateAddressRequest): Promise<ApiResponse<Address>> => {
    try {
      const response = await httpClient.post<ApiResponse<Address>>('/users/addresses', addressData);
      return response;
    } catch (error) {
      return {
        data: null as any,
        success: false,
        message: error instanceof Error ? error.message : 'Failed to create address'
      };
    }
  },

  updateAddress: async (addressId: string, addressData: Partial<CreateAddressRequest>): Promise<ApiResponse<Address>> => {
    try {
      const response = await httpClient.put<ApiResponse<Address>>(`/users/addresses/${addressId}`, addressData);
      return response;
    } catch (error) {
      return {
        data: null as any,
        success: false,
        message: error instanceof Error ? error.message : 'Failed to update address'
      };
    }
  },

  deleteAddress: async (addressId: string): Promise<ApiResponse<{ message: string }>> => {
    try {
      const response = await httpClient.delete<ApiResponse<string>>(`/users/addresses/${addressId}`);
      return {
        data: { message: response.data },
        success: response.success,
        message: response.message
      };
    } catch (error) {
      return {
        data: null as any,
        success: false,
        message: error instanceof Error ? error.message : 'Failed to delete address'
      };
    }
  }
};
