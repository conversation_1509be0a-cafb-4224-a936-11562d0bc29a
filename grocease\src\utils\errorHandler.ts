import { ApiResponse } from '../types';

export interface AppError {
  message: string;
  code?: string;
  statusCode?: number;
  details?: any;
}

export class ApiError extends <PERSON>rror {
  public statusCode: number;
  public code?: string;
  public details?: any;

  constructor(message: string, statusCode: number = 500, code?: string, details?: any) {
    super(message);
    this.name = 'ApiError';
    this.statusCode = statusCode;
    this.code = code;
    this.details = details;
  }
}

export class NetworkError extends Error {
  constructor(message: string = 'Network connection failed') {
    super(message);
    this.name = 'NetworkError';
  }
}

export class ValidationError extends Error {
  public fields: Record<string, string>;

  constructor(message: string, fields: Record<string, string> = {}) {
    super(message);
    this.name = 'ValidationError';
    this.fields = fields;
  }
}

export class AuthenticationError extends Error {
  constructor(message: string = 'Authentication failed') {
    super(message);
    this.name = 'AuthenticationError';
  }
}

export const parseApiError = (error: any): AppError => {
  // Network errors
  if (error.name === 'AbortError') {
    return {
      message: 'Request timeout. Please try again.',
      code: 'TIMEOUT'
    };
  }

  if (error.name === 'TypeError' && error.message.includes('fetch')) {
    return {
      message: 'Network connection failed. Please check your internet connection.',
      code: 'NETWORK_ERROR'
    };
  }

  // API errors
  if (error instanceof ApiError) {
    return {
      message: error.message,
      code: error.code,
      statusCode: error.statusCode,
      details: error.details
    };
  }

  // Validation errors
  if (error instanceof ValidationError) {
    return {
      message: error.message,
      code: 'VALIDATION_ERROR',
      details: error.fields
    };
  }

  // Authentication errors
  if (error instanceof AuthenticationError) {
    return {
      message: error.message,
      code: 'AUTH_ERROR',
      statusCode: 401
    };
  }

  // Generic error
  return {
    message: error.message || 'An unexpected error occurred',
    code: 'UNKNOWN_ERROR'
  };
};

export const getErrorMessage = (error: any): string => {
  const appError = parseApiError(error);
  return appError.message;
};

export const isNetworkError = (error: any): boolean => {
  return error.name === 'TypeError' && error.message.includes('fetch') ||
         error.name === 'AbortError' ||
         error.code === 'NETWORK_ERROR';
};

export const isAuthError = (error: any): boolean => {
  return error instanceof AuthenticationError ||
         error.statusCode === 401 ||
         error.code === 'AUTH_ERROR';
};

export const isValidationError = (error: any): boolean => {
  return error instanceof ValidationError ||
         error.statusCode === 400 ||
         error.code === 'VALIDATION_ERROR';
};

// Helper function to create standardized error responses
export const createErrorResponse = <T>(error: any): ApiResponse<T> => {
  const appError = parseApiError(error);
  
  return {
    data: null as any,
    success: false,
    message: appError.message,
    error: appError.code
  };
};

// Retry utility for failed requests
export const withRetry = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: any;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      
      // Don't retry on authentication or validation errors
      if (isAuthError(error) || isValidationError(error)) {
        throw error;
      }

      // Don't retry on the last attempt
      if (attempt === maxRetries) {
        break;
      }

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }

  throw lastError;
};

// Loading state management
export interface LoadingState {
  isLoading: boolean;
  error: AppError | null;
}

export const createLoadingState = (): LoadingState => ({
  isLoading: false,
  error: null
});

export const setLoading = (state: LoadingState, loading: boolean): LoadingState => ({
  ...state,
  isLoading: loading,
  error: loading ? null : state.error
});

export const setError = (state: LoadingState, error: any): LoadingState => ({
  ...state,
  isLoading: false,
  error: parseApiError(error)
});

export const clearError = (state: LoadingState): LoadingState => ({
  ...state,
  error: null
});
