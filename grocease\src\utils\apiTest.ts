import { api } from '../services/api';
import { authApi } from '../services/authApi';
import { API_CONFIG } from '../constants';

export interface TestResult {
  name: string;
  success: boolean;
  message: string;
  duration: number;
  error?: any;
}

export interface TestSuite {
  name: string;
  results: TestResult[];
  totalTests: number;
  passedTests: number;
  failedTests: number;
  totalDuration: number;
}

class ApiTester {
  private results: TestResult[] = [];

  async runTest(name: string, testFn: () => Promise<void>): Promise<TestResult> {
    const startTime = Date.now();
    
    try {
      await testFn();
      const duration = Date.now() - startTime;
      
      const result: TestResult = {
        name,
        success: true,
        message: 'Test passed',
        duration
      };
      
      this.results.push(result);
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      
      const result: TestResult = {
        name,
        success: false,
        message: error instanceof Error ? error.message : 'Test failed',
        duration,
        error
      };
      
      this.results.push(result);
      return result;
    }
  }

  async testBackendConnection(): Promise<TestResult> {
    return this.runTest('Backend Connection', async () => {
      const response = await fetch(`${API_CONFIG.BASE_URL}/categories`);
      if (!response.ok) {
        throw new Error(`Backend not responding: ${response.status}`);
      }
    });
  }

  async testCategoriesAPI(): Promise<TestResult> {
    return this.runTest('Categories API', async () => {
      const response = await api.getCategories();
      if (!response.success) {
        throw new Error(response.message || 'Categories API failed');
      }
      if (!Array.isArray(response.data)) {
        throw new Error('Categories data is not an array');
      }
    });
  }

  async testProductsAPI(): Promise<TestResult> {
    return this.runTest('Products API', async () => {
      const response = await api.getProducts();
      if (!response.data) {
        throw new Error('Products API failed');
      }
      if (!Array.isArray(response.data)) {
        throw new Error('Products data is not an array');
      }
    });
  }

  async testBannersAPI(): Promise<TestResult> {
    return this.runTest('Banners API', async () => {
      const response = await api.getBanners();
      if (!response.success) {
        throw new Error(response.message || 'Banners API failed');
      }
      if (!Array.isArray(response.data)) {
        throw new Error('Banners data is not an array');
      }
    });
  }

  async testAuthenticationFlow(): Promise<TestResult> {
    return this.runTest('Authentication Flow', async () => {
      // Test registration
      const registerData = {
        name: 'Test User',
        email: `test${Date.now()}@example.com`,
        phone: '+1234567890',
        password: 'password123',
        confirmPassword: 'password123'
      };

      const registerResponse = await authApi.register(registerData);
      if (!registerResponse.success) {
        throw new Error(`Registration failed: ${registerResponse.message}`);
      }

      // Test login with demo credentials
      const loginResponse = await authApi.login({
        email: '<EMAIL>',
        password: 'password123'
      });

      if (!loginResponse.success) {
        throw new Error(`Login failed: ${loginResponse.message}`);
      }

      if (!loginResponse.data.token) {
        throw new Error('No token received from login');
      }
    });
  }

  async testUserProfileAPI(): Promise<TestResult> {
    return this.runTest('User Profile API', async () => {
      // First login to get a token
      const loginResponse = await authApi.login({
        email: '<EMAIL>',
        password: 'password123'
      });

      if (!loginResponse.success) {
        throw new Error('Login required for profile test failed');
      }

      const profileResponse = await api.getUser();
      if (!profileResponse.success) {
        throw new Error(profileResponse.message || 'User profile API failed');
      }

      if (!profileResponse.data.id) {
        throw new Error('User profile missing required fields');
      }
    });
  }

  async testSearchAPI(): Promise<TestResult> {
    return this.runTest('Search API', async () => {
      const response = await api.searchProducts('apple');
      if (!response.success) {
        throw new Error(response.message || 'Search API failed');
      }
      if (!Array.isArray(response.data)) {
        throw new Error('Search results is not an array');
      }
    });
  }

  async testErrorHandling(): Promise<TestResult> {
    return this.runTest('Error Handling', async () => {
      // Test with invalid endpoint
      try {
        await fetch(`${API_CONFIG.BASE_URL}/invalid-endpoint`);
      } catch (error) {
        // This should throw an error, which is expected
      }

      // Test with invalid credentials
      const invalidLoginResponse = await authApi.login({
        email: '<EMAIL>',
        password: 'wrongpassword'
      });

      if (invalidLoginResponse.success) {
        throw new Error('Invalid login should have failed');
      }
    });
  }

  async runAllTests(): Promise<TestSuite> {
    console.log('🧪 Starting API Integration Tests...');
    
    const startTime = Date.now();
    this.results = [];

    // Run all tests
    await this.testBackendConnection();
    await this.testCategoriesAPI();
    await this.testProductsAPI();
    await this.testBannersAPI();
    await this.testAuthenticationFlow();
    await this.testUserProfileAPI();
    await this.testSearchAPI();
    await this.testErrorHandling();

    const totalDuration = Date.now() - startTime;
    const passedTests = this.results.filter(r => r.success).length;
    const failedTests = this.results.filter(r => !r.success).length;

    const suite: TestSuite = {
      name: 'API Integration Tests',
      results: this.results,
      totalTests: this.results.length,
      passedTests,
      failedTests,
      totalDuration
    };

    this.logResults(suite);
    return suite;
  }

  private logResults(suite: TestSuite): void {
    console.log('\n📊 Test Results:');
    console.log(`Total Tests: ${suite.totalTests}`);
    console.log(`✅ Passed: ${suite.passedTests}`);
    console.log(`❌ Failed: ${suite.failedTests}`);
    console.log(`⏱️ Total Duration: ${suite.totalDuration}ms`);
    
    console.log('\n📝 Detailed Results:');
    suite.results.forEach(result => {
      const icon = result.success ? '✅' : '❌';
      console.log(`${icon} ${result.name} (${result.duration}ms)`);
      if (!result.success) {
        console.log(`   Error: ${result.message}`);
      }
    });

    if (suite.failedTests === 0) {
      console.log('\n🎉 All tests passed! API integration is working correctly.');
    } else {
      console.log('\n⚠️ Some tests failed. Please check the backend connection and configuration.');
    }
  }
}

export const apiTester = new ApiTester();

// Export a simple function to run tests
export const runApiTests = () => apiTester.runAllTests();
