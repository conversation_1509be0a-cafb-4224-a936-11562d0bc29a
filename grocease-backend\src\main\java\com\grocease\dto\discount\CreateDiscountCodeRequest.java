package com.grocease.dto.discount;

import com.grocease.entity.DiscountCode;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CreateDiscountCodeRequest {

    @NotBlank(message = "Discount code is required")
    @Size(min = 3, max = 20, message = "Code must be between 3 and 20 characters")
    @Pattern(regexp = "^[A-Z0-9]+$", message = "Code must contain only uppercase letters and numbers")
    private String code;

    @NotBlank(message = "Name is required")
    @Size(max = 100, message = "Name must not exceed 100 characters")
    private String name;

    @Size(max = 500, message = "Description must not exceed 500 characters")
    private String description;

    @NotNull(message = "Discount type is required")
    private DiscountCode.DiscountType type;

    @NotNull(message = "Value is required")
    @DecimalMin(value = "0.01", message = "Value must be greater than 0")
    private BigDecimal value;

    @DecimalMin(value = "0.00", message = "Minimum order amount must be 0 or greater")
    private BigDecimal minimumOrderAmount;

    @DecimalMin(value = "0.01", message = "Maximum discount amount must be greater than 0")
    private BigDecimal maximumDiscountAmount;

    @Min(value = 1, message = "Usage limit must be at least 1")
    private Integer usageLimit;

    @Min(value = 1, message = "Usage limit per user must be at least 1")
    private Integer usageLimitPerUser;

    @NotNull(message = "Valid from date is required")
    @Future(message = "Valid from date must be in the future")
    private LocalDateTime validFrom;

    @NotNull(message = "Valid until date is required")
    @Future(message = "Valid until date must be in the future")
    private LocalDateTime validUntil;

    @NotNull(message = "Active status is required")
    private Boolean isActive;

    private Boolean isFirstOrderOnly = false;

    private List<Long> applicableCategories;

    private List<Long> applicableProducts;

    @AssertTrue(message = "Valid until date must be after valid from date")
    public boolean isValidDateRange() {
        if (validFrom == null || validUntil == null) {
            return true; // Let other validations handle null values
        }
        return validUntil.isAfter(validFrom);
    }

    @AssertTrue(message = "Percentage value must be between 1 and 100")
    public boolean isValidPercentage() {
        if (type == null || type != DiscountCode.DiscountType.PERCENTAGE) {
            return true;
        }
        return value != null && value.compareTo(BigDecimal.ONE) >= 0 && value.compareTo(BigDecimal.valueOf(100)) <= 0;
    }
}
