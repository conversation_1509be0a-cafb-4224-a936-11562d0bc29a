import React, { createContext, useContext, useState } from 'react';
import CustomAlert from '../components/CustomAlert';

interface AlertButton {
  text: string;
  onPress?: () => void;
  style?: 'default' | 'cancel' | 'destructive';
}

interface AlertOptions {
  title: string;
  message?: string;
  buttons?: AlertButton[];
  type?: 'info' | 'success' | 'warning' | 'error';
  icon?: string;
}

interface AlertContextType {
  showAlert: (options: AlertOptions) => void;
  hideAlert: () => void;
}

const AlertContext = createContext<AlertContextType | undefined>(undefined);

export const AlertProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [alertConfig, setAlertConfig] = useState<AlertOptions | null>(null);
  const [visible, setVisible] = useState(false);

  const showAlert = (options: AlertOptions) => {
    setAlertConfig(options);
    setVisible(true);
  };

  const hideAlert = () => {
    setVisible(false);
    setTimeout(() => {
      setAlertConfig(null);
    }, 200); // Wait for animation to complete
  };

  const value: AlertContextType = {
    showAlert,
    hideAlert,
  };

  return (
    <AlertContext.Provider value={value}>
      {children}
      {alertConfig && (
        <CustomAlert
          visible={visible}
          title={alertConfig.title}
          message={alertConfig.message}
          buttons={alertConfig.buttons}
          type={alertConfig.type}
          icon={alertConfig.icon}
          onDismiss={hideAlert}
        />
      )}
    </AlertContext.Provider>
  );
};

export const useCustomAlert = (): AlertContextType => {
  const context = useContext(AlertContext);
  if (context === undefined) {
    throw new Error('useCustomAlert must be used within an AlertProvider');
  }
  return context;
};

// Convenience functions for common alert types
export const useAlert = () => {
  const { showAlert } = useCustomAlert();

  return {
    alert: (title: string, message?: string, buttons?: AlertButton[]) => {
      showAlert({
        title,
        message,
        buttons: buttons || [{ text: 'OK' }],
        type: 'info'
      });
    },
    
    success: (title: string, message?: string, buttons?: AlertButton[]) => {
      showAlert({
        title,
        message,
        buttons: buttons || [{ text: 'OK' }],
        type: 'success'
      });
    },
    
    warning: (title: string, message?: string, buttons?: AlertButton[]) => {
      showAlert({
        title,
        message,
        buttons: buttons || [{ text: 'OK' }],
        type: 'warning'
      });
    },
    
    error: (title: string, message?: string, buttons?: AlertButton[]) => {
      showAlert({
        title,
        message,
        buttons: buttons || [{ text: 'OK' }],
        type: 'error'
      });
    },
    
    confirm: (
      title: string, 
      message?: string, 
      onConfirm?: () => void, 
      onCancel?: () => void,
      confirmText: string = 'Confirm',
      cancelText: string = 'Cancel'
    ) => {
      showAlert({
        title,
        message,
        buttons: [
          { text: cancelText, style: 'cancel', onPress: onCancel },
          { text: confirmText, style: 'default', onPress: onConfirm }
        ],
        type: 'warning'
      });
    },
    
    destructive: (
      title: string, 
      message?: string, 
      onConfirm?: () => void, 
      onCancel?: () => void,
      confirmText: string = 'Delete',
      cancelText: string = 'Cancel'
    ) => {
      showAlert({
        title,
        message,
        buttons: [
          { text: cancelText, style: 'cancel', onPress: onCancel },
          { text: confirmText, style: 'destructive', onPress: onConfirm }
        ],
        type: 'error'
      });
    }
  };
};
