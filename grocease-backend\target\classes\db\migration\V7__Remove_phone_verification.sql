-- Remove phone verification related fields and data
-- This migration removes phone number fields and phone verification functionality

-- Remove phone-related columns from users table
ALTER TABLE users DROP COLUMN IF EXISTS phone;
ALTER TABLE users DROP COLUMN IF EXISTS is_phone_verified;

-- Remove phone verification OTP tokens
DELETE FROM otp_tokens WHERE type = 'PHONE_VERIFICATION';

-- Update OTP type enum to remove PHONE_VERIFICATION (MySQL specific)
-- Note: In production, you might want to handle this differently based on your database

-- Clean up any orphaned OTP tokens
DELETE FROM otp_tokens WHERE expires_at < NOW();

-- Add index for better email lookup performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);

-- Update any existing users to have email verification enabled if not already
UPDATE users SET is_email_verified = true WHERE is_email_verified IS NULL;
