import { Product, Category, Banner, User, Address } from '../types';

export const mockCategories: Category[] = [
  {
    id: '1',
    name: 'Fresh Produce',
    image: 'https://images.unsplash.com/photo-1542838132-92c53300491e?w=400',
    icon: '🥬',
    color: '#22c55e'
  },
  {
    id: '2',
    name: 'Dairy & Eggs',
    image: 'https://images.unsplash.com/photo-**********-e9143da7973b?w=400',
    icon: '🥛',
    color: '#3b82f6'
  },
  {
    id: '3',
    name: 'Meat & Seafood',
    image: 'https://images.unsplash.com/photo-1529692236671-f1f6cf9683ba?w=400',
    icon: '🥩',
    color: '#ef4444'
  },
  {
    id: '4',
    name: '<PERSON><PERSON>',
    image: 'https://images.unsplash.com/photo-1509440159596-0249088772ff?w=400',
    icon: '🍞',
    color: '#f59e0b'
  },
  {
    id: '5',
    name: 'Pan<PERSON>',
    image: 'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=400',
    icon: '🥫',
    color: '#8b5cf6'
  },
  {
    id: '6',
    name: 'Beverages',
    image: 'https://images.unsplash.com/photo-1544145945-f90425340c7e?w=400',
    icon: '🥤',
    color: '#06b6d4'
  }
];

export const mockBanners: Banner[] = [
  {
    id: '1',
    title: 'Fresh Deals',
    subtitle: 'Up to 30% off on fresh produce',
    image: 'https://images.unsplash.com/photo-1542838132-92c53300491e?w=800',
    backgroundColor: '#22c55e',
    textColor: '#ffffff',
    actionText: 'Shop Now'
  },
  {
    id: '2',
    title: 'Free Delivery',
    subtitle: 'On orders above $50',
    image: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800',
    backgroundColor: '#f59e0b',
    textColor: '#ffffff',
    actionText: 'Order Now'
  }
];

export const mockProducts: Product[] = [
  {
    id: '1',
    name: 'Organic Bananas',
    description: 'Fresh organic bananas, perfect for smoothies and snacks',
    price: 2.99,
    originalPrice: 3.49,
    discount: 14,
    image: 'https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?w=400',
    category: '1',
    unit: 'per bunch',
    inStock: true,
    rating: 4.5,
    reviewCount: 128,
    tags: ['organic', 'fresh', 'healthy']
  },
  {
    id: '2',
    name: 'Fresh Milk',
    description: 'Whole milk from local farms, rich in calcium and protein',
    price: 4.29,
    image: 'https://images.unsplash.com/photo-**********-e9143da7973b?w=400',
    category: '2',
    unit: '1 gallon',
    inStock: true,
    rating: 4.8,
    reviewCount: 89,
    tags: ['fresh', 'local', 'protein']
  },
  {
    id: '3',
    name: 'Salmon Fillet',
    description: 'Fresh Atlantic salmon, rich in omega-3 fatty acids',
    price: 12.99,
    originalPrice: 15.99,
    discount: 19,
    image: 'https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2?w=400',
    category: '3',
    unit: 'per lb',
    inStock: true,
    rating: 4.7,
    reviewCount: 56,
    tags: ['fresh', 'omega-3', 'protein']
  },
  {
    id: '4',
    name: 'Sourdough Bread',
    description: 'Artisan sourdough bread, baked fresh daily',
    price: 5.99,
    image: 'https://images.unsplash.com/photo-1509440159596-0249088772ff?w=400',
    category: '4',
    unit: 'per loaf',
    inStock: true,
    rating: 4.6,
    reviewCount: 234,
    tags: ['artisan', 'fresh', 'daily-baked']
  },
  {
    id: '5',
    name: 'Organic Apples',
    description: 'Crisp organic Honeycrisp apples',
    price: 4.99,
    image: 'https://images.unsplash.com/photo-1560806887-1e4cd0b6cbd6?w=400',
    category: '1',
    unit: 'per bag (3 lbs)',
    inStock: true,
    rating: 4.4,
    reviewCount: 167,
    tags: ['organic', 'crisp', 'sweet']
  },
  {
    id: '6',
    name: 'Greek Yogurt',
    description: 'Creamy Greek yogurt, high in protein',
    price: 6.49,
    originalPrice: 7.99,
    discount: 19,
    image: 'https://images.unsplash.com/photo-1488477181946-6428a0291777?w=400',
    category: '2',
    unit: '32 oz container',
    inStock: true,
    rating: 4.9,
    reviewCount: 312,
    tags: ['protein', 'creamy', 'healthy']
  }
];

export const mockUser: User = {
  id: '1',
  name: 'John Doe',
  email: '<EMAIL>',
  phone: '+****************',
  avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400',
  addresses: [
    {
      id: '1',
      type: 'home',
      street: '123 Main Street',
      city: 'San Francisco',
      state: 'CA',
      zipCode: '94102',
      isDefault: true
    },
    {
      id: '2',
      type: 'work',
      street: '456 Business Ave',
      city: 'San Francisco',
      state: 'CA',
      zipCode: '94105',
      isDefault: false
    }
  ]
};
