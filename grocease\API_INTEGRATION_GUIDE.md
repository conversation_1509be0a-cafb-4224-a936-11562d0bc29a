# API Integration Guide

This guide explains how to test and validate the API integration between the mobile app and backend.

## 🚀 Quick Start

### 1. Start the Backend Server

Navigate to the backend directory and start the server:

```bash
cd grocease-backend
./run-app.bat  # Windows
# or
mvn spring-boot:run  # Cross-platform
```

The backend should be running on `http://localhost:8080`

### 2. Test API Integration

In the mobile app, you can test the integration in several ways:

#### Option A: Use the API Test Component
```typescript
import { ApiTestComponent } from './src/components/ApiTestComponent';

// Add this to any screen for testing
<ApiTestComponent />
```

#### Option B: Run Tests Programmatically
```typescript
import { runApiTests } from './src/utils/apiTest';

// Run all tests
const results = await runApiTests();
console.log(results);
```

#### Option C: Test Individual APIs
```typescript
import { api } from './src/services/api';
import { authApi } from './src/services/authApi';

// Test categories
const categories = await api.getCategories();

// Test authentication
const loginResult = await authApi.login({
  email: '<EMAIL>',
  password: 'password123'
});
```

## 🔧 Configuration

### API Base URL
The API base URL is configured in `src/constants/index.ts`:

```typescript
export const API_CONFIG = {
  BASE_URL: __DEV__ ? 'http://localhost:8080/api' : 'https://api.grocease.com/api',
  TIMEOUT: 10000,
  RETRY_ATTEMPTS: 3,
};
```

### For Android Emulator
If testing on Android emulator, you may need to use `********` instead of `localhost`:

```typescript
BASE_URL: __DEV__ ? 'http://********:8080/api' : 'https://api.grocease.com/api',
```

### For Physical Device
If testing on a physical device, use your computer's IP address:

```typescript
BASE_URL: __DEV__ ? 'http://*************:8080/api' : 'https://api.grocease.com/api',
```

## 📋 API Endpoints Integrated

### Authentication
- ✅ `POST /auth/login` - User login
- ✅ `POST /auth/register` - User registration
- ✅ `POST /auth/forgot-password` - Password reset
- ✅ `POST /auth/verify-otp` - OTP verification
- ✅ `POST /auth/reset-password` - Password reset
- ✅ `POST /auth/refresh-token` - Token refresh

### Products & Categories
- ✅ `GET /categories` - Get all categories
- ✅ `GET /products` - Get products (with pagination, search, filtering)
- ✅ `GET /products/{id}` - Get product details
- ✅ `GET /products/featured` - Get featured products

### Orders
- ✅ `POST /orders` - Create order
- ✅ `GET /orders` - Get user orders (paginated)
- ✅ `GET /orders/{id}` - Get order details

### User Management
- ✅ `GET /users/profile` - Get user profile
- ✅ `PUT /users/profile` - Update user profile
- ✅ `GET /users/addresses` - Get user addresses
- ✅ `POST /users/addresses` - Create address
- ✅ `PUT /users/addresses/{id}` - Update address
- ✅ `DELETE /users/addresses/{id}` - Delete address

### Content
- ✅ `GET /banners` - Get promotional banners

## 🧪 Testing Checklist

### Backend Connection
- [ ] Backend server is running on port 8080
- [ ] Can access `http://localhost:8080/api/categories` in browser
- [ ] No CORS errors in browser console

### Authentication Flow
- [ ] User registration works
- [ ] User login works with demo credentials
- [ ] JWT token is received and stored
- [ ] Protected endpoints work with token
- [ ] Token refresh works

### Data Flow
- [ ] Categories load correctly
- [ ] Products load with pagination
- [ ] Product search works
- [ ] Banners load correctly
- [ ] Order creation works
- [ ] Order history loads

### Error Handling
- [ ] Network errors are handled gracefully
- [ ] API errors show appropriate messages
- [ ] Loading states work correctly
- [ ] Retry mechanism works for failed requests

## 🐛 Troubleshooting

### Common Issues

#### 1. "Network request failed"
- Check if backend is running
- Verify API base URL is correct
- Check firewall settings
- For Android emulator, use `********` instead of `localhost`

#### 2. "CORS error"
- Backend CORS is configured for `http://localhost:3000`
- Mobile apps shouldn't have CORS issues
- If testing in web browser, check CORS configuration

#### 3. "401 Unauthorized"
- Check if JWT token is being sent correctly
- Verify token hasn't expired
- Check if user is logged in

#### 4. "Connection timeout"
- Increase timeout in `API_CONFIG`
- Check network connectivity
- Verify backend is responding

### Debug Steps

1. **Check Backend Logs**
   ```bash
   # In grocease-backend directory
   tail -f logs/application.log
   ```

2. **Enable Network Debugging**
   ```typescript
   // Add to httpClient.ts for debugging
   console.log('Request:', url, config);
   console.log('Response:', response);
   ```

3. **Test with Postman/curl**
   ```bash
   # Test categories endpoint
   curl http://localhost:8080/api/categories
   
   # Test login
   curl -X POST http://localhost:8080/api/auth/login \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","password":"password123"}'
   ```

## 📱 Demo Credentials

For testing authentication:
- **Email**: `<EMAIL>`
- **Password**: `password123`

## 🎯 Next Steps

After successful integration:

1. **Remove Test Components** - Remove `ApiTestComponent` from production builds
2. **Update Error Messages** - Customize error messages for better UX
3. **Add Loading Indicators** - Implement proper loading states in UI
4. **Optimize Performance** - Add caching and optimize API calls
5. **Add Offline Support** - Implement offline functionality
6. **Security Review** - Review token storage and API security

## 📞 Support

If you encounter issues:
1. Check this guide first
2. Review backend logs
3. Test individual API endpoints
4. Check network configuration
5. Verify backend database is set up correctly
