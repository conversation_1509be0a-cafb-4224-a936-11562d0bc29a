-- Create discount_codes table
CREATE TABLE discount_codes (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(20) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    type ENUM('PERCENTAGE', 'FIXED_AMOUNT', 'FREE_DELIVERY') NOT NULL,
    value DECIMAL(10,2) NOT NULL,
    minimum_order_amount DECIMAL(10,2),
    maximum_discount_amount DECIMAL(10,2),
    usage_limit INT,
    usage_limit_per_user INT,
    used_count INT DEFAULT 0,
    valid_from DATETIME NOT NULL,
    valid_until DATETIME NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    is_first_order_only BOOLEAN DEFAULT FALSE,
    applicable_categories JSON,
    applicable_products JSON,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create discount_code_usages table
CREATE TABLE discount_code_usages (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    discount_code_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    order_id BIGINT NOT NULL,
    discount_amount DECIMAL(10,2) NOT NULL,
    order_subtotal DECIMAL(10,2) NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (discount_code_id) REFERENCES discount_codes(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    UNIQUE KEY unique_discount_per_order (discount_code_id, order_id)
);

-- Add discount_code column to orders table
ALTER TABLE orders ADD COLUMN discount_code VARCHAR(20);

-- Create indexes for better performance
CREATE INDEX idx_discount_codes_code ON discount_codes(code);
CREATE INDEX idx_discount_codes_active_valid ON discount_codes(is_active, valid_from, valid_until);
CREATE INDEX idx_discount_code_usages_user ON discount_code_usages(user_id);
CREATE INDEX idx_discount_code_usages_code ON discount_code_usages(discount_code_id);
CREATE INDEX idx_orders_discount_code ON orders(discount_code);

-- Insert sample discount codes for testing
INSERT INTO discount_codes (code, name, description, type, value, minimum_order_amount, valid_from, valid_until, is_active, is_first_order_only) VALUES
('WELCOME10', 'Welcome Discount', 'Get 10% off on your first order', 'PERCENTAGE', 10.00, 25.00, NOW(), DATE_ADD(NOW(), INTERVAL 1 YEAR), TRUE, TRUE),
('SAVE5', 'Save $5', 'Get $5 off on orders above $30', 'FIXED_AMOUNT', 5.00, 30.00, NOW(), DATE_ADD(NOW(), INTERVAL 6 MONTH), TRUE, FALSE),
('FREEDELIVERY', 'Free Delivery', 'Get free delivery on any order', 'FREE_DELIVERY', 0.00, 0.00, NOW(), DATE_ADD(NOW(), INTERVAL 3 MONTH), TRUE, FALSE),
('FRUIT20', 'Fruit Special', 'Get 20% off on fruits', 'PERCENTAGE', 20.00, 15.00, NOW(), DATE_ADD(NOW(), INTERVAL 1 MONTH), TRUE, FALSE);
