package com.grocease.controller;

import com.grocease.dto.ApiResponse;
import com.grocease.dto.discount.ApplyDiscountRequest;
import com.grocease.dto.discount.DiscountCalculationResult;
import com.grocease.dto.discount.DiscountCodeDto;
import com.grocease.entity.User;
import com.grocease.service.DiscountCodeService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/discount-codes")
@RequiredArgsConstructor
@Slf4j
public class DiscountCodeController {

    private final DiscountCodeService discountCodeService;

    @PostMapping("/apply")
    public ResponseEntity<ApiResponse<DiscountCalculationResult>> applyDiscountCode(
            @AuthenticationPrincipal User user,
            @Valid @RequestBody ApplyDiscountRequest request) {
        log.info("User {} applying discount code: {}", user.getId(), request.getCode());
        
        DiscountCalculationResult result = discountCodeService.applyDiscountCode(user.getId(), request);
        
        if (result.getIsValid()) {
            return ResponseEntity.ok(ApiResponse.success(result, "Discount code applied successfully"));
        } else {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Invalid discount code", result.getMessage()));
        }
    }

    @GetMapping("/available")
    public ResponseEntity<ApiResponse<List<DiscountCodeDto>>> getAvailableDiscountCodes() {
        log.info("Getting available discount codes");
        List<DiscountCodeDto> codes = discountCodeService.getAvailableDiscountCodes();
        return ResponseEntity.ok(ApiResponse.success(codes, "Available discount codes retrieved successfully"));
    }

    @GetMapping("/validate/{code}")
    public ResponseEntity<ApiResponse<DiscountCodeDto>> validateDiscountCode(
            @PathVariable String code) {
        log.info("Validating discount code: {}", code);
        
        try {
            DiscountCodeDto discountCode = discountCodeService.getDiscountCodeByCode(code);
            return ResponseEntity.ok(ApiResponse.success(discountCode, "Discount code is valid"));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Invalid discount code", e.getMessage()));
        }
    }
}
