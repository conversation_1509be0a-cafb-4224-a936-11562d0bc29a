import React from 'react';
import { View, Text, ScrollView, Animated } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';

import { formatDate } from '../utils';
import Button from '../components/Button';

type OrderConfirmationRouteProp = RouteProp<{
  OrderConfirmation: { orderId: string };
}, 'OrderConfirmation'>;

const OrderConfirmationScreen = () => {
  const navigation = useNavigation();
  const route = useRoute<OrderConfirmationRouteProp>();
  const { orderId } = route.params;

  const scaleValue = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    Animated.spring(scaleValue, {
      toValue: 1,
      tension: 50,
      friction: 7,
      useNativeDriver: true,
    }).start();
  }, []);

  const handleContinueShopping = () => {
    navigation.navigate('Main' as never);
  };

  const handleTrackOrder = () => {
    // In a real app, this would navigate to order tracking
    navigation.navigate('Main' as never);
  };

  return (
    <SafeAreaView className="flex-1 bg-primary-50">
      <ScrollView className="flex-1" contentContainerStyle={{ flexGrow: 1 }}>
        <View className="flex-1 px-6 py-12">
          {/* Success Animation */}
          <View className="items-center mb-8">
            <Animated.View
              style={{
                transform: [{ scale: scaleValue }],
              }}
              className="w-32 h-32 bg-primary-500 rounded-full items-center justify-center mb-6"
            >
              <Ionicons name="checkmark" size={64} color="#ffffff" />
            </Animated.View>

            <Text className="text-3xl font-bold text-neutral-800 text-center mb-4">
              Order Confirmed!
            </Text>

            <Text className="text-lg text-neutral-600 text-center mb-2">
              Thank you for your order
            </Text>

            <Text className="text-base text-neutral-500 text-center">
              Order #{orderId}
            </Text>
          </View>

          {/* Order Details */}
          <View className="bg-white rounded-2xl p-6 shadow-sm border border-neutral-100 mb-6">
            <Text className="text-xl font-bold text-neutral-800 mb-4">
              Order Details
            </Text>

            <View className="space-y-4">
              <View className="flex-row items-center">
                <View className="w-10 h-10 bg-primary-100 rounded-full items-center justify-center mr-4">
                  <Ionicons name="time" size={20} color="#22c55e" />
                </View>
                <View className="flex-1">
                  <Text className="text-base font-semibold text-neutral-800">
                    Estimated Delivery
                  </Text>
                  <Text className="text-sm text-neutral-600">
                    {formatDate(new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString())}
                  </Text>
                </View>
              </View>

              <View className="flex-row items-center">
                <View className="w-10 h-10 bg-blue-100 rounded-full items-center justify-center mr-4">
                  <Ionicons name="location" size={20} color="#3b82f6" />
                </View>
                <View className="flex-1">
                  <Text className="text-base font-semibold text-neutral-800">
                    Delivery Address
                  </Text>
                  <Text className="text-sm text-neutral-600">
                    Your selected delivery address
                  </Text>
                </View>
              </View>

              <View className="flex-row items-center">
                <View className="w-10 h-10 bg-accent-100 rounded-full items-center justify-center mr-4">
                  <Ionicons name="card" size={20} color="#f59e0b" />
                </View>
                <View className="flex-1">
                  <Text className="text-base font-semibold text-neutral-800">
                    Payment Method
                  </Text>
                  <Text className="text-sm text-neutral-600">
                    Cash on Delivery
                  </Text>
                </View>
              </View>
            </View>
          </View>

          {/* What's Next */}
          <View className="bg-white rounded-2xl p-6 shadow-sm border border-neutral-100 mb-8">
            <Text className="text-xl font-bold text-neutral-800 mb-4">
              What's Next?
            </Text>

            <View className="space-y-4">
              <View className="flex-row">
                <View className="w-8 h-8 bg-primary-500 rounded-full items-center justify-center mr-4 mt-1">
                  <Text className="text-sm font-bold text-white">1</Text>
                </View>
                <View className="flex-1">
                  <Text className="text-base font-semibold text-neutral-800">
                    Order Confirmation
                  </Text>
                  <Text className="text-sm text-neutral-600">
                    We've received your order and will start preparing it shortly.
                  </Text>
                </View>
              </View>

              <View className="flex-row">
                <View className="w-8 h-8 bg-neutral-300 rounded-full items-center justify-center mr-4 mt-1">
                  <Text className="text-sm font-bold text-neutral-600">2</Text>
                </View>
                <View className="flex-1">
                  <Text className="text-base font-semibold text-neutral-800">
                    Preparing Your Order
                  </Text>
                  <Text className="text-sm text-neutral-600">
                    Our team will carefully pick and pack your items.
                  </Text>
                </View>
              </View>

              <View className="flex-row">
                <View className="w-8 h-8 bg-neutral-300 rounded-full items-center justify-center mr-4 mt-1">
                  <Text className="text-sm font-bold text-neutral-600">3</Text>
                </View>
                <View className="flex-1">
                  <Text className="text-base font-semibold text-neutral-800">
                    Out for Delivery
                  </Text>
                  <Text className="text-sm text-neutral-600">
                    Your order is on its way to your doorstep.
                  </Text>
                </View>
              </View>

              <View className="flex-row">
                <View className="w-8 h-8 bg-neutral-300 rounded-full items-center justify-center mr-4 mt-1">
                  <Text className="text-sm font-bold text-neutral-600">4</Text>
                </View>
                <View className="flex-1">
                  <Text className="text-base font-semibold text-neutral-800">
                    Delivered
                  </Text>
                  <Text className="text-sm text-neutral-600">
                    Enjoy your fresh groceries!
                  </Text>
                </View>
              </View>
            </View>
          </View>

          {/* Support */}
          <View className="bg-accent-50 rounded-2xl p-6 border border-accent-200">
            <View className="flex-row items-center mb-3">
              <Ionicons name="headset" size={24} color="#f59e0b" />
              <Text className="text-lg font-bold text-accent-800 ml-3">
                Need Help?
              </Text>
            </View>
            <Text className="text-base text-accent-700 mb-4">
              Our customer support team is here to help you with any questions about your order.
            </Text>
            <Text className="text-sm text-accent-600">
              Call us at: +1 (555) 123-4567{'\n'}
              Email: <EMAIL>
            </Text>
          </View>
        </View>
      </ScrollView>

      {/* Bottom Actions */}
      <View className="bg-white border-t border-neutral-200 px-6 py-4">
        <View className="space-y-3">
          <Button
            title="Track Your Order"
            onPress={handleTrackOrder}
            size="lg"
            fullWidth
          />
          <Button
            title="Continue Shopping"
            onPress={handleContinueShopping}
            variant="outline"
            size="lg"
            fullWidth
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

export default OrderConfirmationScreen;
