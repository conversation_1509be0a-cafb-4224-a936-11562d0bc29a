import React from 'react';
import {
  View,
  Text,
  FlatList,
  RefreshControl,
  TouchableOpacity
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { useQuery } from '@tanstack/react-query';
import { Ionicons } from '@expo/vector-icons';

import { api } from '../services/api';
import { Category } from '../types';
import CategoryCard from '../components/CategoryCard';
import LoadingSpinner from '../components/LoadingSpinner';
import Button from '../components/Button';

const CategoriesScreen = () => {
  const navigation = useNavigation();

  // Fetch categories
  const {
    data: categoriesData,
    isLoading,
    refetch,
    error
  } = useQuery({
    queryKey: ['categories'],
    queryFn: api.getCategories,
  });

  const handleCategoryPress = (category: Category) => {
    navigation.navigate('ProductList' as never, {
      categoryId: category.id,
      categoryName: category.name
    } as never);
  };

  const handleSearchPress = () => {
    navigation.navigate('Search' as never);
  };

  const renderCategoryItem = ({ item, index }: { item: Category; index: number }) => (
    <View className={`w-1/2 ${index % 2 === 0 ? 'pr-2' : 'pl-2'} mb-4`}>
      <CategoryCard category={item} onPress={handleCategoryPress} />
    </View>
  );

  const renderHeader = () => (
    <View className="mb-6">
      <Text className="text-2xl font-bold text-neutral-800 mb-2">
        Shop by Category
      </Text>
      <Text className="text-base text-neutral-600 mb-4">
        Browse our wide selection of fresh groceries and essentials
      </Text>

      {/* Search Bar */}
      <TouchableOpacity
        className="flex-row items-center bg-neutral-100 rounded-2xl px-4 py-3"
        onPress={handleSearchPress}
      >
        <Ionicons name="search" size={20} color="#64748b" />
        <Text className="flex-1 ml-3 text-base text-neutral-500">
          Search for products...
        </Text>
      </TouchableOpacity>
    </View>
  );

  const renderEmptyState = () => (
    <View className="flex-1 items-center justify-center px-6">
      <Ionicons name="grid-outline" size={80} color="#cbd5e1" />
      <Text className="text-2xl font-bold text-neutral-800 mt-6 mb-2">
        No Categories Available
      </Text>
      <Text className="text-base text-neutral-600 text-center mb-6">
        We're having trouble loading categories. Please try again.
      </Text>
      <Button
        title="Retry"
        onPress={() => refetch()}
        variant="primary"
        size="md"
      />
    </View>
  );

  const renderQuickActions = () => (
    <View className="mb-6">
      <Text className="text-lg font-bold text-neutral-800 mb-4">
        Quick Actions
      </Text>

      <View className="flex-row space-x-3">
        <TouchableOpacity className="flex-1 bg-primary-50 rounded-2xl p-4 border border-primary-200">
          <View className="items-center">
            <View className="w-12 h-12 bg-primary-500 rounded-full items-center justify-center mb-2">
              <Ionicons name="flash" size={24} color="#ffffff" />
            </View>
            <Text className="text-sm font-semibold text-primary-800">
              Express Delivery
            </Text>
            <Text className="text-xs text-primary-600 text-center">
              Under 30 mins
            </Text>
          </View>
        </TouchableOpacity>

        <TouchableOpacity className="flex-1 bg-accent-50 rounded-2xl p-4 border border-accent-200">
          <View className="items-center">
            <View className="w-12 h-12 bg-accent-500 rounded-full items-center justify-center mb-2">
              <Ionicons name="gift" size={24} color="#ffffff" />
            </View>
            <Text className="text-sm font-semibold text-accent-800">
              Daily Deals
            </Text>
            <Text className="text-xs text-accent-600 text-center">
              Save up to 50%
            </Text>
          </View>
        </TouchableOpacity>

        <TouchableOpacity className="flex-1 bg-blue-50 rounded-2xl p-4 border border-blue-200">
          <View className="items-center">
            <View className="w-12 h-12 bg-blue-500 rounded-full items-center justify-center mb-2">
              <Ionicons name="leaf" size={24} color="#ffffff" />
            </View>
            <Text className="text-sm font-semibold text-blue-800">
              Organic
            </Text>
            <Text className="text-xs text-blue-600 text-center">
              Fresh & Natural
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );

  if (isLoading) {
    return <LoadingSpinner message="Loading categories..." fullScreen />;
  }

  if (error || !categoriesData?.data) {
    return (
      <SafeAreaView className="flex-1 bg-neutral-50">
        {renderEmptyState()}
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-neutral-50">
      <View className="flex-1 px-4 pt-6">
        <FlatList
          data={categoriesData.data}
          renderItem={renderCategoryItem}
          keyExtractor={(item) => item.id}
          numColumns={2}
          ListHeaderComponent={
            <View>
              {renderHeader()}
              {renderQuickActions()}
            </View>
          }
          refreshControl={
            <RefreshControl refreshing={isLoading} onRefresh={refetch} />
          }
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: 20 }}
        />
      </View>
    </SafeAreaView>
  );
};

export default CategoriesScreen;
