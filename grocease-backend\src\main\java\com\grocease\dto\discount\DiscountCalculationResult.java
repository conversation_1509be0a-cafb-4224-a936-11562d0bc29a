package com.grocease.dto.discount;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DiscountCalculationResult {
    private Boolean isValid;
    private String message;
    private BigDecimal discountAmount;
    private BigDecimal originalSubtotal;
    private BigDecimal originalDeliveryFee;
    private BigDecimal finalSubtotal;
    private BigDecimal finalDeliveryFee;
    private BigDecimal finalTotal;
    private String discountCode;
    private String discountDescription;
}
