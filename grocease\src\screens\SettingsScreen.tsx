import React from 'react';
import { 
  View, 
  Text, 
  ScrollView, 
  TouchableOpacity,
  Alert,
  Switch
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

import { useNotifications } from '../hooks/useNotifications';
import { useLocation } from '../hooks/useLocation';
import Button from '../components/Button';

const SettingsScreen = () => {
  const { 
    permissionStatus, 
    requestPermission, 
    sendTestNotification,
    expoPushToken 
  } = useNotifications();
  
  const { 
    location, 
    loading: locationLoading, 
    error: locationError, 
    requestLocation 
  } = useLocation();

  const [notificationsEnabled, setNotificationsEnabled] = React.useState(
    permissionStatus === 'granted'
  );

  React.useEffect(() => {
    setNotificationsEnabled(permissionStatus === 'granted');
  }, [permissionStatus]);

  const handleNotificationToggle = async (value: boolean) => {
    if (value && permissionStatus !== 'granted') {
      const granted = await requestPermission();
      if (granted) {
        setNotificationsEnabled(true);
        Alert.alert(
          'Notifications Enabled',
          'You will now receive order updates and promotional offers.',
          [{ text: 'OK' }]
        );
      } else {
        Alert.alert(
          'Permission Denied',
          'Please enable notifications in your device settings to receive order updates.',
          [{ text: 'OK' }]
        );
      }
    } else {
      setNotificationsEnabled(value);
      if (!value) {
        Alert.alert(
          'Notifications Disabled',
          'You will no longer receive push notifications. You can re-enable them anytime in settings.',
          [{ text: 'OK' }]
        );
      }
    }
  };

  const handleTestNotification = async () => {
    if (permissionStatus !== 'granted') {
      Alert.alert(
        'Permission Required',
        'Please enable notifications first to test this feature.',
        [{ text: 'OK' }]
      );
      return;
    }

    try {
      await sendTestNotification();
      Alert.alert(
        'Test Notification Sent',
        'A test notification will appear in 2 seconds.',
        [{ text: 'OK' }]
      );
    } catch (error) {
      Alert.alert(
        'Error',
        'Failed to send test notification. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  const handleLocationTest = () => {
    if (location) {
      Alert.alert(
        'Current Location',
        `Latitude: ${location.latitude.toFixed(6)}\nLongitude: ${location.longitude.toFixed(6)}\n\nAddress: ${location.address}\nCity: ${location.city}, ${location.state}`,
        [
          { text: 'Refresh Location', onPress: requestLocation },
          { text: 'OK' }
        ]
      );
    } else if (locationError) {
      Alert.alert(
        'Location Error',
        locationError,
        [
          { text: 'Try Again', onPress: requestLocation },
          { text: 'OK' }
        ]
      );
    } else {
      requestLocation();
    }
  };

  const renderSettingItem = (
    icon: string,
    title: string,
    subtitle: string,
    rightElement?: React.ReactNode,
    onPress?: () => void
  ) => (
    <TouchableOpacity
      className="bg-white rounded-2xl p-4 shadow-sm border border-neutral-100 mb-3"
      onPress={onPress}
      disabled={!onPress}
    >
      <View className="flex-row items-center">
        <View className="w-10 h-10 bg-neutral-100 rounded-full items-center justify-center mr-4">
          <Ionicons name={icon as any} size={20} color="#64748b" />
        </View>
        <View className="flex-1">
          <Text className="text-base font-semibold text-neutral-800">
            {title}
          </Text>
          <Text className="text-sm text-neutral-600 mt-1">
            {subtitle}
          </Text>
        </View>
        {rightElement}
      </View>
    </TouchableOpacity>
  );

  const getPermissionStatusText = () => {
    switch (permissionStatus) {
      case 'granted':
        return 'Enabled';
      case 'denied':
        return 'Disabled';
      default:
        return 'Not determined';
    }
  };

  const getPermissionStatusColor = () => {
    switch (permissionStatus) {
      case 'granted':
        return 'text-primary-600';
      case 'denied':
        return 'text-red-600';
      default:
        return 'text-neutral-600';
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-neutral-50">
      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        <View className="px-4 py-6">
          {/* Header */}
          <View className="mb-6">
            <Text className="text-2xl font-bold text-neutral-800 mb-2">
              Settings & Permissions
            </Text>
            <Text className="text-base text-neutral-600">
              Manage your app preferences and test features
            </Text>
          </View>

          {/* Notifications Section */}
          <View className="mb-6">
            <Text className="text-lg font-bold text-neutral-800 mb-4 px-2">
              Push Notifications
            </Text>
            
            {renderSettingItem(
              'notifications-outline',
              'Enable Notifications',
              `Status: ${getPermissionStatusText()}`,
              <Switch
                value={notificationsEnabled}
                onValueChange={handleNotificationToggle}
                trackColor={{ false: '#e2e8f0', true: '#bbf7d0' }}
                thumbColor={notificationsEnabled ? '#22c55e' : '#94a3b8'}
              />
            )}

            {/* Notification Details */}
            <View className="bg-white rounded-2xl p-4 shadow-sm border border-neutral-100 mb-3">
              <Text className="text-base font-semibold text-neutral-800 mb-3">
                Notification Details
              </Text>
              <View className="space-y-2">
                <View className="flex-row justify-between">
                  <Text className="text-sm text-neutral-600">Permission Status:</Text>
                  <Text className={`text-sm font-medium ${getPermissionStatusColor()}`}>
                    {getPermissionStatusText()}
                  </Text>
                </View>
                {expoPushToken && (
                  <View className="flex-row justify-between">
                    <Text className="text-sm text-neutral-600">Push Token:</Text>
                    <Text className="text-sm text-neutral-800 font-mono">
                      {expoPushToken.substring(0, 20)}...
                    </Text>
                  </View>
                )}
              </View>
            </View>

            {/* Test Notification Button */}
            <View className="px-2">
              <Button
                title="Send Test Notification"
                onPress={handleTestNotification}
                variant="outline"
                size="md"
                fullWidth
              />
            </View>
          </View>

          {/* Location Section */}
          <View className="mb-6">
            <Text className="text-lg font-bold text-neutral-800 mb-4 px-2">
              Location Services
            </Text>
            
            {renderSettingItem(
              'location-outline',
              'GPS Location',
              location 
                ? `${location.city}, ${location.state}` 
                : locationError 
                  ? 'Location access denied'
                  : 'Getting location...',
              undefined,
              handleLocationTest
            )}

            {/* Location Details */}
            <View className="bg-white rounded-2xl p-4 shadow-sm border border-neutral-100 mb-3">
              <Text className="text-base font-semibold text-neutral-800 mb-3">
                Location Details
              </Text>
              {locationLoading ? (
                <Text className="text-sm text-neutral-600">Getting location...</Text>
              ) : location ? (
                <View className="space-y-2">
                  <View className="flex-row justify-between">
                    <Text className="text-sm text-neutral-600">Address:</Text>
                    <Text className="text-sm text-neutral-800 flex-1 text-right">
                      {location.address}
                    </Text>
                  </View>
                  <View className="flex-row justify-between">
                    <Text className="text-sm text-neutral-600">City:</Text>
                    <Text className="text-sm text-neutral-800">
                      {location.city}, {location.state}
                    </Text>
                  </View>
                  <View className="flex-row justify-between">
                    <Text className="text-sm text-neutral-600">Coordinates:</Text>
                    <Text className="text-sm text-neutral-800 font-mono">
                      {location.latitude.toFixed(4)}, {location.longitude.toFixed(4)}
                    </Text>
                  </View>
                </View>
              ) : (
                <Text className="text-sm text-red-600">
                  {locationError || 'Location not available'}
                </Text>
              )}
            </View>

            {/* Refresh Location Button */}
            <View className="px-2">
              <Button
                title="Refresh Location"
                onPress={requestLocation}
                variant="outline"
                size="md"
                fullWidth
                loading={locationLoading}
              />
            </View>
          </View>

          {/* App Info */}
          <View className="mb-6">
            <Text className="text-lg font-bold text-neutral-800 mb-4 px-2">
              App Information
            </Text>
            
            <View className="bg-white rounded-2xl p-4 shadow-sm border border-neutral-100">
              <Text className="text-base font-semibold text-neutral-800 mb-3">
                GroceEase
              </Text>
              <View className="space-y-2">
                <View className="flex-row justify-between">
                  <Text className="text-sm text-neutral-600">Version:</Text>
                  <Text className="text-sm text-neutral-800">1.0.0</Text>
                </View>
                <View className="flex-row justify-between">
                  <Text className="text-sm text-neutral-600">Build:</Text>
                  <Text className="text-sm text-neutral-800">2024.01.01</Text>
                </View>
                <View className="flex-row justify-between">
                  <Text className="text-sm text-neutral-600">Platform:</Text>
                  <Text className="text-sm text-neutral-800">React Native</Text>
                </View>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default SettingsScreen;
