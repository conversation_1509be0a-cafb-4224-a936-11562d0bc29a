export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  discount?: number;
  image: string;
  category: string; // Backend returns category name, not ID
  unit: string;
  inStock: boolean;
  rating: number;
  reviewCount: number;
  tags: string[];
}

export interface Category {
  id: string;
  name: string;
  image?: string;
  icon?: string;
  color?: string;
  isActive: boolean;
  sortOrder?: number;
}

export interface CartItem {
  product: Product;
  quantity: number;
}

export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role?: string;
  isEmailVerified: boolean;
  isActive: boolean;
  createdAt: string;
  addresses?: Address[];
}

export interface Address {
  id: string;
  type: 'HOME' | 'WORK' | 'OTHER';
  street: string;
  city: string;
  state: string;
  zipCode: string;
  isDefault: boolean;
}

export interface OrderItem {
  product: Product;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
}

export interface Order {
  id: string;
  orderNumber?: string;
  total: number;
  subtotal: number;
  deliveryFee: number;
  discount: number;
  discountCode?: string;
  status: 'PENDING' | 'CONFIRMED' | 'PREPARING' | 'OUT_FOR_DELIVERY' | 'DELIVERED' | 'CANCELLED';
  deliveryAddress: Address;
  orderDate: string;
  estimatedDelivery?: string;
  items: OrderItem[];
}

export interface Banner {
  id: string;
  title: string;
  subtitle?: string;
  image: string;
  backgroundColor?: string;
  textColor?: string;
  actionText?: string;
  actionUrl?: string;
  isActive: boolean;
  sortOrder?: number;
  createdAt?: string;
}

export interface Location {
  latitude: number;
  longitude: number;
  address: string;
  city: string;
  state: string;
}

export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Authentication Types
export interface AuthUser {
  id: string;
  email: string;
  name: string;
  phone?: string;
  avatar?: string;
  role?: string;
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  isActive: boolean;
  createdAt: string;
  addresses?: Address[];
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  name: string;
  email: string;
  phone: string;
  password: string;
  confirmPassword: string;
}

export interface AuthResponse {
  user: AuthUser;
  token: string;
  refreshToken: string;
  expiresIn: number;
}

export interface ForgotPasswordData {
  email: string;
}

export interface ResetPasswordData {
  token: string;
  password: string;
  confirmPassword: string;
}

export interface OTPVerificationData {
  email: string;
  otp: string;
  type: 'email_verification' | 'password_reset' | 'phone_verification';
}

export interface AuthState {
  user: AuthUser | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

// Request Types
export interface CreateOrderRequest {
  items: {
    productId: number;
    quantity: number;
  }[];
  deliveryAddressId: number;
  total: number;
  subtotal: number;
  deliveryFee: number;
  discount?: number;
  discountCode?: string;
}

export interface CreateAddressRequest {
  type: 'HOME' | 'WORK' | 'OTHER';
  street: string;
  city: string;
  state: string;
  zipCode: string;
  isDefault?: boolean;
}

export interface UpdateUserRequest {
  name?: string;
  phone?: string;
  avatar?: string;
}
