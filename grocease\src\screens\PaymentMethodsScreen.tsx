import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  FlatList
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../hooks/useTheme';
import { useAlert } from '../hooks/useCustomAlert';
import Button from '../components/Button';

interface PaymentMethod {
  id: string;
  type: 'card' | 'paypal' | 'apple_pay' | 'google_pay';
  title: string;
  subtitle: string;
  last4?: string;
  expiryDate?: string;
  isDefault: boolean;
  icon: string;
}

const PaymentMethodsScreen = () => {
  const navigation = useNavigation();
  const { isDark } = useTheme();
  const { alert, confirm, destructive } = useAlert();
  
  // Mock data - in real app, this would come from API/state management
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([
    {
      id: '1',
      type: 'card',
      title: 'Visa ending in 4242',
      subtitle: 'Expires 12/25',
      last4: '4242',
      expiryDate: '12/25',
      isDefault: true,
      icon: 'card'
    },
    {
      id: '2',
      type: 'paypal',
      title: 'PayPal',
      subtitle: '<EMAIL>',
      isDefault: false,
      icon: 'logo-paypal'
    },
    {
      id: '3',
      type: 'apple_pay',
      title: 'Apple Pay',
      subtitle: 'Touch ID or Face ID',
      isDefault: false,
      icon: 'logo-apple'
    }
  ]);

  const handleAddPaymentMethod = () => {
    // For now, just show a simple alert. In a real app, this would show a selection modal
    alert(
      'Add Payment Method',
      'Choose a payment method to add:\n\n• Credit/Debit Card\n• PayPal\n• Apple Pay\n• Google Pay'
    );
  };

  const handleEditPaymentMethod = (method: PaymentMethod) => {
    alert('Edit Payment Method', `Editing ${method.title}`);
  };

  const handleDeletePaymentMethod = (methodId: string) => {
    destructive(
      'Remove Payment Method',
      'Are you sure you want to remove this payment method?',
      () => {
        setPaymentMethods(prev => prev.filter(method => method.id !== methodId));
      },
      undefined,
      'Remove',
      'Cancel'
    );
  };

  const handleSetDefault = (methodId: string) => {
    setPaymentMethods(prev => 
      prev.map(method => ({
        ...method,
        isDefault: method.id === methodId
      }))
    );
  };

  const getMethodColor = (type: string) => {
    switch (type) {
      case 'card':
        return '#3b82f6';
      case 'paypal':
        return '#0070ba';
      case 'apple_pay':
        return '#000000';
      case 'google_pay':
        return '#4285f4';
      default:
        return '#64748b';
    }
  };

  const renderPaymentMethodItem = ({ item }: { item: PaymentMethod }) => (
    <View className={`rounded-2xl p-4 mb-3 ${isDark ? 'bg-neutral-800' : 'bg-white'} shadow-sm border ${isDark ? 'border-neutral-700' : 'border-neutral-100'}`}>
      <View className="flex-row items-start justify-between mb-3">
        <View className="flex-row items-center flex-1">
          <View className={`w-12 h-12 rounded-xl items-center justify-center mr-3`} style={{ backgroundColor: `${getMethodColor(item.type)}15` }}>
            <Ionicons 
              name={item.icon as any} 
              size={24} 
              color={getMethodColor(item.type)} 
            />
          </View>
          <View className="flex-1">
            <View className="flex-row items-center">
              <Text className={`text-lg font-semibold ${isDark ? 'text-neutral-100' : 'text-neutral-800'}`}>
                {item.title}
              </Text>
              {item.isDefault && (
                <View className="ml-2 px-2 py-1 bg-primary-100 rounded-full">
                  <Text className="text-primary-700 text-xs font-medium">Default</Text>
                </View>
              )}
            </View>
            <Text className={`text-sm mt-1 ${isDark ? 'text-neutral-400' : 'text-neutral-600'}`}>
              {item.subtitle}
            </Text>
          </View>
        </View>
      </View>

      <View className="flex-row space-x-2">
        {!item.isDefault && (
          <TouchableOpacity
            onPress={() => handleSetDefault(item.id)}
            className={`flex-1 py-2 px-3 rounded-lg border ${isDark ? 'border-neutral-600 bg-neutral-700' : 'border-neutral-300 bg-neutral-50'}`}
          >
            <Text className={`text-center text-sm font-medium ${isDark ? 'text-neutral-200' : 'text-neutral-700'}`}>
              Set Default
            </Text>
          </TouchableOpacity>
        )}
        
        {item.type === 'card' && (
          <TouchableOpacity
            onPress={() => handleEditPaymentMethod(item)}
            className="flex-1 py-2 px-3 rounded-lg bg-primary-100 border border-primary-200"
          >
            <Text className="text-center text-sm font-medium text-primary-700">
              Edit
            </Text>
          </TouchableOpacity>
        )}
        
        <TouchableOpacity
          onPress={() => handleDeletePaymentMethod(item.id)}
          className={`py-2 px-3 rounded-lg border ${isDark ? 'border-error-800 bg-error-900/20' : 'border-error-200 bg-error-50'}`}
        >
          <Ionicons name="trash-outline" size={16} color="#ef4444" />
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <SafeAreaView className={`flex-1 ${isDark ? 'bg-neutral-900' : 'bg-neutral-50'}`}>
      {/* Header */}
      <View className={`px-4 py-4 border-b ${isDark ? 'bg-neutral-800 border-neutral-700' : 'bg-white border-neutral-200'}`}>
        <View className="flex-row items-center justify-between">
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            className="w-10 h-10 items-center justify-center"
          >
            <Ionicons 
              name="arrow-back" 
              size={24} 
              color={isDark ? '#f5f5f5' : '#1e293b'} 
            />
          </TouchableOpacity>
          <Text className={`text-lg font-bold ${isDark ? 'text-neutral-100' : 'text-neutral-800'}`}>
            Payment Methods
          </Text>
          <TouchableOpacity
            onPress={handleAddPaymentMethod}
            className="w-10 h-10 items-center justify-center"
          >
            <Ionicons 
              name="add" 
              size={24} 
              color={isDark ? '#f5f5f5' : '#1e293b'} 
            />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView className="flex-1 px-4 py-6" showsVerticalScrollIndicator={false}>
        {/* Security Info */}
        <View className={`rounded-2xl p-4 mb-6 ${isDark ? 'bg-success-900/20 border-success-800' : 'bg-success-50 border-success-200'} border`}>
          <View className="flex-row items-start">
            <Ionicons 
              name="shield-checkmark" 
              size={20} 
              color={isDark ? '#4ade80' : '#22c55e'} 
              style={{ marginTop: 2 }} 
            />
            <View className="flex-1 ml-3">
              <Text className={`text-sm font-medium ${isDark ? 'text-success-200' : 'text-success-800'}`}>
                Secure Payments
              </Text>
              <Text className={`text-sm mt-1 ${isDark ? 'text-success-300' : 'text-success-700'}`}>
                Your payment information is encrypted and secure. We never store your full card details.
              </Text>
            </View>
          </View>
        </View>

        {/* Payment Methods List */}
        {paymentMethods.length > 0 ? (
          <FlatList
            data={paymentMethods}
            renderItem={renderPaymentMethodItem}
            keyExtractor={(item) => item.id}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
          />
        ) : (
          <View className="items-center py-12">
            <View className={`w-20 h-20 rounded-full items-center justify-center mb-4 ${isDark ? 'bg-neutral-800' : 'bg-neutral-100'}`}>
              <Ionicons 
                name="card-outline" 
                size={32} 
                color={isDark ? '#a3a3a3' : '#64748b'} 
              />
            </View>
            <Text className={`text-lg font-semibold mb-2 ${isDark ? 'text-neutral-200' : 'text-neutral-800'}`}>
              No Payment Methods
            </Text>
            <Text className={`text-center mb-6 ${isDark ? 'text-neutral-400' : 'text-neutral-600'}`}>
              Add a payment method to make checkout faster and easier.
            </Text>
            <Button
              title="Add Payment Method"
              onPress={handleAddPaymentMethod}
              variant="primary"
              size="md"
            />
          </View>
        )}

        {/* Add Payment Method Button (when methods exist) */}
        {paymentMethods.length > 0 && (
          <Button
            title="Add Payment Method"
            onPress={handleAddPaymentMethod}
            variant="outline"
            size="lg"
            className="mt-4"
          />
        )}

        {/* Accepted Payment Types */}
        <View className={`rounded-2xl p-4 mt-6 ${isDark ? 'bg-neutral-800' : 'bg-white'} border ${isDark ? 'border-neutral-700' : 'border-neutral-100'}`}>
          <Text className={`text-sm font-medium mb-3 ${isDark ? 'text-neutral-200' : 'text-neutral-700'}`}>
            Accepted Payment Methods
          </Text>
          <View className="flex-row flex-wrap">
            {[
              { name: 'Visa', icon: 'card' },
              { name: 'Mastercard', icon: 'card' },
              { name: 'PayPal', icon: 'logo-paypal' },
              { name: 'Apple Pay', icon: 'logo-apple' },
              { name: 'Google Pay', icon: 'logo-google' }
            ].map((method, index) => (
              <View key={index} className={`flex-row items-center mr-4 mb-2 px-2 py-1 rounded-lg ${isDark ? 'bg-neutral-700' : 'bg-neutral-50'}`}>
                <Ionicons 
                  name={method.icon as any} 
                  size={16} 
                  color={isDark ? '#d4d4d4' : '#64748b'} 
                />
                <Text className={`text-xs ml-1 ${isDark ? 'text-neutral-300' : 'text-neutral-600'}`}>
                  {method.name}
                </Text>
              </View>
            ))}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default PaymentMethodsScreen;
