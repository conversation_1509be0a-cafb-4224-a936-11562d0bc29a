package com.grocease.dto.discount;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ApplyDiscountRequest {

    @NotBlank(message = "Discount code is required")
    private String code;

    @NotNull(message = "Order subtotal is required")
    @Positive(message = "Order subtotal must be positive")
    private BigDecimal orderSubtotal;

    @NotNull(message = "Delivery fee is required")
    private BigDecimal deliveryFee;

    private List<CartItemDto> items;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class CartItemDto {
        @NotNull(message = "Product ID is required")
        private Long productId;

        @NotNull(message = "Category ID is required")
        private Long categoryId;

        @NotNull(message = "Quantity is required")
        @Positive(message = "Quantity must be positive")
        private Integer quantity;

        @NotNull(message = "Unit price is required")
        @Positive(message = "Unit price must be positive")
        private BigDecimal unitPrice;
    }
}
