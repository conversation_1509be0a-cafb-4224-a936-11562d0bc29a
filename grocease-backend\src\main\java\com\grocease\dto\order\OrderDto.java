package com.grocease.dto.order;

import com.grocease.dto.user.AddressDto;
import com.grocease.entity.Order;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OrderDto {
    private String id;
    private String orderNumber;
    private BigDecimal total;
    private BigDecimal subtotal;
    private BigDecimal deliveryFee;
    private BigDecimal discount;
    private String discountCode;
    private Order.OrderStatus status;
    private AddressDto deliveryAddress;
    private LocalDateTime orderDate;
    private LocalDateTime estimatedDelivery;
    private List<OrderItemDto> items;
}
