package com.grocease.dto.discount;

import com.grocease.entity.DiscountCode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DiscountCodeDto {
    private String id;
    private String code;
    private String name;
    private String description;
    private DiscountCode.DiscountType type;
    private BigDecimal value;
    private BigDecimal minimumOrderAmount;
    private BigDecimal maximumDiscountAmount;
    private Integer usageLimit;
    private Integer usageLimitPerUser;
    private Integer usedCount;
    private LocalDateTime validFrom;
    private LocalDateTime validUntil;
    private Boolean isActive;
    private Boolean isFirstOrderOnly;
    private List<String> applicableCategories;
    private List<String> applicableProducts;
    private LocalDateTime createdAt;
    private Boolean isValid;
    private Boolean isExpired;
}
