import React from 'react';
import { View, Text, Image, Dimensions } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';

import Button from '../components/Button';

const { width, height } = Dimensions.get('window');

const OnboardingScreen = () => {
  const navigation = useNavigation();

  const handleGetStarted = () => {
    navigation.navigate('Main' as never);
  };

  return (
    <SafeAreaView className="flex-1 bg-gradient-to-b from-primary-50 to-white">
      <View className="flex-1 px-6">
        {/* Header with Logo */}
        <View className="items-center mt-8 mb-12">
          <View className="w-20 h-20 bg-primary-500 rounded-2xl items-center justify-center mb-4">
            <Ionicons name="basket" size={40} color="#ffffff" />
          </View>
          <Text className="text-3xl font-bold text-neutral-800">
            GroceEase
          </Text>
          <Text className="text-lg text-neutral-600 mt-2">
            Fresh groceries at your doorstep
          </Text>
        </View>

        {/* Main Illustration */}
        <View className="flex-1 justify-center items-center">
          <View className="w-80 h-80 bg-primary-100 rounded-full items-center justify-center mb-8">
            <View className="w-64 h-64 bg-primary-200 rounded-full items-center justify-center">
              <Ionicons name="storefront" size={120} color="#22c55e" />
            </View>
          </View>
        </View>

        {/* Value Proposition */}
        <View className="mb-8">
          <Text className="text-2xl font-bold text-neutral-800 text-center mb-4">
            Everything you need,{'\n'}delivered fresh
          </Text>
          <Text className="text-base text-neutral-600 text-center leading-6">
            Shop from thousands of products including fresh produce, dairy, meat, and pantry essentials. Get everything delivered to your door in under 2 hours.
          </Text>
        </View>

        {/* Features */}
        <View className="mb-8">
          <View className="flex-row items-center mb-4">
            <View className="w-10 h-10 bg-primary-100 rounded-full items-center justify-center mr-4">
              <Ionicons name="flash" size={20} color="#22c55e" />
            </View>
            <Text className="text-base text-neutral-700 flex-1">
              Fast delivery in under 2 hours
            </Text>
          </View>

          <View className="flex-row items-center mb-4">
            <View className="w-10 h-10 bg-accent-100 rounded-full items-center justify-center mr-4">
              <Ionicons name="leaf" size={20} color="#f59e0b" />
            </View>
            <Text className="text-base text-neutral-700 flex-1">
              Fresh, organic, and locally sourced
            </Text>
          </View>

          <View className="flex-row items-center mb-4">
            <View className="w-10 h-10 bg-blue-100 rounded-full items-center justify-center mr-4">
              <Ionicons name="shield-checkmark" size={20} color="#3b82f6" />
            </View>
            <Text className="text-base text-neutral-700 flex-1">
              Safe and contactless delivery
            </Text>
          </View>
        </View>

        {/* Get Started Button */}
        <View className="mb-8">
          <Button
            title="Get Started"
            onPress={handleGetStarted}
            size="lg"
            fullWidth
          />
          <Text className="text-sm text-neutral-500 text-center mt-4">
            By continuing, you agree to our Terms of Service and Privacy Policy
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default OnboardingScreen;
