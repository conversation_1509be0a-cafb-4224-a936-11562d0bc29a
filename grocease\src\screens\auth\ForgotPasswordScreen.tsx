import React, { useState } from 'react';
import { 
  View, 
  Text, 
  TextInput, 
  TouchableOpacity, 
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';

import { useAuth } from '../../hooks/useAuth';
import { validateEmail } from '../../utils';
import Button from '../../components/Button';

const ForgotPasswordScreen = () => {
  const navigation = useNavigation();
  const { forgotPassword } = useAuth();
  
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSendOTP = async () => {
    // Validation
    if (!email.trim()) {
      Alert.alert('Validation Error', 'Please enter your email address');
      return;
    }

    if (!validateEmail(email)) {
      Alert.alert('Validation Error', 'Please enter a valid email address');
      return;
    }

    setIsLoading(true);
    try {
      const result = await forgotPassword({ email: email.trim().toLowerCase() });

      if (result.success) {
        Alert.alert(
          'OTP Sent',
          'A verification code has been sent to your email address. Please check your inbox.',
          [
            {
              text: 'OK',
              onPress: () => {
                navigation.navigate('OTPVerification' as never, {
                  email: email.trim().toLowerCase(),
                  type: 'password_reset'
                } as never);
              }
            }
          ]
        );
      } else {
        Alert.alert('Error', result.message);
      }
    } catch (error) {
      Alert.alert('Error', 'Something went wrong. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackToLogin = () => {
    navigation.navigate('Login' as never);
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <KeyboardAvoidingView 
        className="flex-1" 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
          <View className="px-6 py-8">
            {/* Back Button */}
            <TouchableOpacity 
              className="w-10 h-10 rounded-full bg-neutral-100 items-center justify-center mb-8"
              onPress={() => navigation.goBack()}
            >
              <Ionicons name="arrow-back" size={20} color="#64748b" />
            </TouchableOpacity>

            {/* Header */}
            <View className="items-center mb-8">
              <View className="w-20 h-20 bg-accent-500 rounded-2xl items-center justify-center mb-4">
                <Ionicons name="lock-closed" size={40} color="#ffffff" />
              </View>
              <Text className="text-3xl font-bold text-neutral-800 mb-2">
                Forgot Password?
              </Text>
              <Text className="text-base text-neutral-600 text-center">
                Don't worry! Enter your email address and we'll send you a verification code to reset your password.
              </Text>
            </View>

            {/* Email Input Form */}
            <View className="space-y-6">
              {/* Email Input */}
              <View>
                <Text className="text-base font-semibold text-neutral-800 mb-2">
                  Email Address
                </Text>
                <View className="relative">
                  <TextInput
                    className="bg-neutral-100 rounded-lg px-4 py-3 pr-12 text-base text-neutral-800"
                    placeholder="Enter your email address"
                    placeholderTextColor="#94a3b8"
                    value={email}
                    onChangeText={setEmail}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    autoCorrect={false}
                    autoFocus
                  />
                  <View className="absolute right-4 top-3">
                    <Ionicons name="mail" size={20} color="#94a3b8" />
                  </View>
                </View>
              </View>

              {/* Send OTP Button */}
              <Button
                title="Send Verification Code"
                onPress={handleSendOTP}
                loading={isLoading}
                size="lg"
                fullWidth
              />

              {/* Instructions */}
              <View className="bg-blue-50 rounded-2xl p-4 border border-blue-200">
                <View className="flex-row items-center mb-2">
                  <Ionicons name="information-circle" size={20} color="#3b82f6" />
                  <Text className="text-sm font-semibold text-blue-800 ml-2">
                    What happens next?
                  </Text>
                </View>
                <View className="space-y-2">
                  <View className="flex-row items-start">
                    <Text className="text-blue-700 mr-2">1.</Text>
                    <Text className="text-sm text-blue-700 flex-1">
                      We'll send a 6-digit verification code to your email
                    </Text>
                  </View>
                  <View className="flex-row items-start">
                    <Text className="text-blue-700 mr-2">2.</Text>
                    <Text className="text-sm text-blue-700 flex-1">
                      Enter the code on the next screen
                    </Text>
                  </View>
                  <View className="flex-row items-start">
                    <Text className="text-blue-700 mr-2">3.</Text>
                    <Text className="text-sm text-blue-700 flex-1">
                      Create a new password for your account
                    </Text>
                  </View>
                </View>
              </View>

              {/* Demo Info */}
              <View className="bg-accent-50 rounded-2xl p-4 border border-accent-200">
                <View className="flex-row items-center mb-2">
                  <Ionicons name="bulb" size={20} color="#f59e0b" />
                  <Text className="text-sm font-semibold text-accent-800 ml-2">
                    Demo Mode
                  </Text>
                </View>
                <Text className="text-sm text-accent-700">
                  In demo mode, the OTP will be displayed in the console. 
                  Use email: <EMAIL> for testing.
                </Text>
              </View>

              {/* Back to Login */}
              <View className="flex-row justify-center items-center mt-8">
                <Text className="text-neutral-600">
                  Remember your password? 
                </Text>
                <TouchableOpacity onPress={handleBackToLogin} className="ml-1">
                  <Text className="text-primary-600 font-semibold">
                    Sign In
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default ForgotPasswordScreen;
