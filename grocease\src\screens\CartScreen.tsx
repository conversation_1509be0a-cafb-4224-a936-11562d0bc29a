import React from 'react';
import {
  View,
  Text,
  ScrollView,
  FlatList,
  Alert
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';

import { useCart } from '../hooks/useCart';
import { formatPrice } from '../utils';
import { FREE_DELIVERY_THRESHOLD } from '../constants';

import CartItemCard from '../components/CartItemCard';
import Button from '../components/Button';

const CartScreen = () => {
  const navigation = useNavigation();
  const { items, updateQuantity, removeItem, clearCart, getCartSummary } = useCart();

  const cartSummary = getCartSummary();
  const { subtotal, deliveryFee, total, itemCount } = cartSummary;

  const handleQuantityChange = (productId: string, quantity: number) => {
    updateQuantity(productId, quantity);
  };

  const handleRemoveItem = (productId: string) => {
    const item = items.find(item => item.product.id === productId);
    if (item) {
      Alert.alert(
        'Remove Item',
        `Remove ${item.product.name} from cart?`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Remove',
            style: 'destructive',
            onPress: () => removeItem(productId)
          }
        ]
      );
    }
  };

  const handleClearCart = () => {
    Alert.alert(
      'Clear Cart',
      'Are you sure you want to remove all items from your cart?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear All',
          style: 'destructive',
          onPress: clearCart
        }
      ]
    );
  };

  const handleCheckout = () => {
    navigation.navigate('Checkout' as never);
  };

  const handleContinueShopping = () => {
    navigation.navigate('Main' as never);
  };

  const renderCartItem = ({ item }: { item: typeof items[0] }) => (
    <CartItemCard
      item={item}
      onQuantityChange={handleQuantityChange}
      onRemove={handleRemoveItem}
    />
  );

  const renderEmptyCart = () => (
    <View className="flex-1 items-center justify-center px-6">
      <Ionicons name="bag-outline" size={80} color="#cbd5e1" />
      <Text className="text-2xl font-bold text-neutral-800 mt-6 mb-2">
        Your cart is empty
      </Text>
      <Text className="text-base text-neutral-600 text-center mb-8">
        Add some delicious items to your cart and they will appear here.
      </Text>
      <Button
        title="Start Shopping"
        onPress={handleContinueShopping}
        size="lg"
      />
    </View>
  );

  const renderPriceSummary = () => (
    <View className="bg-white rounded-2xl p-6 shadow-sm border border-neutral-100">
      <Text className="text-lg font-bold text-neutral-800 mb-4">
        Order Summary
      </Text>

      <View className="space-y-3">
        <View className="flex-row justify-between">
          <Text className="text-base text-neutral-700">
            Subtotal ({itemCount} items)
          </Text>
          <Text className="text-base font-semibold text-neutral-800">
            {formatPrice(subtotal)}
          </Text>
        </View>

        <View className="flex-row justify-between">
          <Text className="text-base text-neutral-700">
            Delivery Fee
          </Text>
          <Text className={`text-base font-semibold ${
            deliveryFee === 0 ? 'text-primary-600' : 'text-neutral-800'
          }`}>
            {deliveryFee === 0 ? 'FREE' : formatPrice(deliveryFee)}
          </Text>
        </View>

        {subtotal < FREE_DELIVERY_THRESHOLD && deliveryFee > 0 && (
          <View className="bg-accent-50 rounded-lg p-3">
            <Text className="text-sm text-accent-700">
              Add {formatPrice(FREE_DELIVERY_THRESHOLD - subtotal)} more for free delivery!
            </Text>
          </View>
        )}

        <View className="border-t border-neutral-200 pt-3">
          <View className="flex-row justify-between">
            <Text className="text-lg font-bold text-neutral-800">
              Total
            </Text>
            <Text className="text-lg font-bold text-neutral-800">
              {formatPrice(total)}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );

  if (items.length === 0) {
    return (
      <SafeAreaView className="flex-1 bg-neutral-50">
        {renderEmptyCart()}
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-neutral-50">
      <View className="flex-1">
        {/* Header */}
        <View className="bg-white px-4 py-4 shadow-sm">
          <View className="flex-row items-center justify-between">
            <Text className="text-xl font-bold text-neutral-800">
              Shopping Cart ({itemCount})
            </Text>
            <Button
              title="Clear All"
              onPress={handleClearCart}
              variant="outline"
              size="sm"
            />
          </View>
        </View>

        {/* Cart Items */}
        <View className="flex-1 px-4 pt-4">
          <FlatList
            data={items}
            renderItem={renderCartItem}
            keyExtractor={(item) => item.product.id}
            showsVerticalScrollIndicator={false}
            ListFooterComponent={
              <View className="mb-4">
                {renderPriceSummary()}
              </View>
            }
          />
        </View>

        {/* Bottom Actions */}
        <View className="bg-white border-t border-neutral-200 px-4 py-4">
          <View className="flex-row space-x-3">
            <Button
              title="Continue Shopping"
              onPress={handleContinueShopping}
              variant="outline"
              size="lg"
              style={{ flex: 1 }}
            />
            <Button
              title={`Checkout • ${formatPrice(total)}`}
              onPress={handleCheckout}
              size="lg"
              style={{ flex: 2 }}
            />
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default CartScreen;
