#!/usr/bin/env node

/**
 * API Integration Validation Script
 * 
 * This script validates that the mobile app can connect to the backend API
 * Run this before testing the mobile app to ensure everything is set up correctly
 */

const https = require('https');
const http = require('http');

const API_BASE_URL = 'http://localhost:8080/api';
const TIMEOUT = 5000;

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const protocol = url.startsWith('https') ? https : http;
    
    const req = protocol.request(url, {
      timeout: TIMEOUT,
      ...options
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const parsed = JSON.parse(data);
          resolve({ status: res.statusCode, data: parsed, headers: res.headers });
        } catch (e) {
          resolve({ status: res.statusCode, data, headers: res.headers });
        }
      });
    });

    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

async function testEndpoint(name, url, expectedStatus = 200, options = {}) {
  try {
    log(`Testing ${name}...`, colors.blue);
    const response = await makeRequest(url, options);
    
    if (response.status === expectedStatus) {
      log(`✅ ${name} - OK (${response.status})`, colors.green);
      return true;
    } else {
      log(`❌ ${name} - Unexpected status: ${response.status}`, colors.red);
      return false;
    }
  } catch (error) {
    log(`❌ ${name} - Error: ${error.message}`, colors.red);
    return false;
  }
}

async function testAuthFlow() {
  try {
    log('Testing Authentication Flow...', colors.blue);
    
    // Test login
    const loginData = JSON.stringify({
      email: '<EMAIL>',
      password: 'password123'
    });

    const response = await makeRequest(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: loginData
    });

    if (response.status === 200 && response.data.success) {
      log('✅ Authentication Flow - Login successful', colors.green);
      return response.data.data.token;
    } else {
      log(`❌ Authentication Flow - Login failed: ${response.data.message || 'Unknown error'}`, colors.red);
      return null;
    }
  } catch (error) {
    log(`❌ Authentication Flow - Error: ${error.message}`, colors.red);
    return null;
  }
}

async function testProtectedEndpoint(token) {
  if (!token) {
    log('❌ Protected Endpoint - No token available', colors.red);
    return false;
  }

  try {
    log('Testing Protected Endpoint...', colors.blue);
    
    const response = await makeRequest(`${API_BASE_URL}/users/profile`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (response.status === 200) {
      log('✅ Protected Endpoint - User profile accessible', colors.green);
      return true;
    } else {
      log(`❌ Protected Endpoint - Failed: ${response.status}`, colors.red);
      return false;
    }
  } catch (error) {
    log(`❌ Protected Endpoint - Error: ${error.message}`, colors.red);
    return false;
  }
}

async function main() {
  log(`${colors.bold}🧪 API Integration Validation${colors.reset}\n`);
  log(`Testing backend at: ${API_BASE_URL}\n`);

  const results = [];

  // Test basic connectivity
  results.push(await testEndpoint('Backend Health Check', `${API_BASE_URL}/categories`));
  
  // Test public endpoints
  results.push(await testEndpoint('Categories API', `${API_BASE_URL}/categories`));
  results.push(await testEndpoint('Products API', `${API_BASE_URL}/products`));
  results.push(await testEndpoint('Banners API', `${API_BASE_URL}/banners`));
  
  // Test authentication
  const token = await testAuthFlow();
  results.push(!!token);
  
  // Test protected endpoints
  results.push(await testProtectedEndpoint(token));

  // Summary
  const passed = results.filter(Boolean).length;
  const total = results.length;
  
  log('\n' + '='.repeat(50));
  log(`${colors.bold}Test Summary:${colors.reset}`);
  log(`Total Tests: ${total}`);
  log(`Passed: ${passed}`, passed === total ? colors.green : colors.yellow);
  log(`Failed: ${total - passed}`, total - passed === 0 ? colors.green : colors.red);
  
  if (passed === total) {
    log('\n🎉 All tests passed! The API integration is ready.', colors.green);
    log('\nYou can now:');
    log('1. Start the mobile app with: npm start', colors.blue);
    log('2. Test the integration in the app', colors.blue);
    log('3. Use the ApiTestComponent for detailed testing', colors.blue);
  } else {
    log('\n⚠️  Some tests failed. Please check:', colors.yellow);
    log('1. Backend server is running (./run-app.bat)', colors.yellow);
    log('2. Database is set up correctly', colors.yellow);
    log('3. No firewall blocking port 8080', colors.yellow);
    log('4. Check backend logs for errors', colors.yellow);
  }
  
  log('\n📖 For detailed setup instructions, see API_INTEGRATION_GUIDE.md');
}

// Handle uncaught errors
process.on('uncaughtException', (error) => {
  log(`\n❌ Unexpected error: ${error.message}`, colors.red);
  process.exit(1);
});

process.on('unhandledRejection', (error) => {
  log(`\n❌ Unhandled promise rejection: ${error.message}`, colors.red);
  process.exit(1);
});

// Run the validation
main().catch((error) => {
  log(`\n❌ Validation failed: ${error.message}`, colors.red);
  process.exit(1);
});
