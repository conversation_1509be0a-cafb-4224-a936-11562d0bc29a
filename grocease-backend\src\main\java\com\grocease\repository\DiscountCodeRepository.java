package com.grocease.repository;

import com.grocease.entity.DiscountCode;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface DiscountCodeRepository extends JpaRepository<DiscountCode, Long> {

    Optional<DiscountCode> findByCodeIgnoreCase(String code);

    boolean existsByCodeIgnoreCase(String code);

    @Query("SELECT d FROM DiscountCode d WHERE d.isActive = true AND d.validFrom <= :now AND d.validUntil >= :now")
    List<DiscountCode> findActiveDiscountCodes(@Param("now") LocalDateTime now);

    @Query("SELECT d FROM DiscountCode d WHERE d.isActive = true AND d.validFrom <= :now AND d.validUntil >= :now AND (d.usageLimit IS NULL OR d.usedCount < d.usageLimit)")
    List<DiscountCode> findAvailableDiscountCodes(@Param("now") LocalDateTime now);

    Page<DiscountCode> findByIsActiveTrueOrderByCreatedAtDesc(Pageable pageable);

    Page<DiscountCode> findAllByOrderByCreatedAtDesc(Pageable pageable);

    @Query("SELECT d FROM DiscountCode d WHERE d.validUntil < :now AND d.isActive = true")
    List<DiscountCode> findExpiredActiveCodes(@Param("now") LocalDateTime now);

    @Query("SELECT COUNT(d) FROM DiscountCode d WHERE d.isActive = true")
    Long countActiveCodes();

    @Query("SELECT COUNT(d) FROM DiscountCode d WHERE d.validFrom <= :now AND d.validUntil >= :now AND d.isActive = true")
    Long countCurrentlyValidCodes(@Param("now") LocalDateTime now);
}
