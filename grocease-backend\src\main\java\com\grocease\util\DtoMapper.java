package com.grocease.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.grocease.dto.banner.BannerDto;
import com.grocease.dto.discount.DiscountCodeDto;
import com.grocease.dto.order.OrderDto;
import com.grocease.dto.order.OrderItemDto;
import com.grocease.dto.product.CategoryDto;
import com.grocease.dto.product.ProductDto;
import com.grocease.dto.user.AddressDto;
import com.grocease.dto.user.UserDto;
import com.grocease.entity.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@Slf4j
public class DtoMapper {

    private final ObjectMapper objectMapper;

    public UserDto toUserDto(User user) {
        return UserDto.builder()
                .id(String.valueOf(user.getId()))
                .name(user.getName())
                .email(user.getEmail())
                .phone(user.getPhone())
                .avatar(user.getAvatar())
                .role(user.getRole().name())
                .isEmailVerified(user.getIsEmailVerified())
                .isPhoneVerified(user.getIsPhoneVerified())
                .isActive(user.getIsActive())
                .createdAt(user.getCreatedAt())
                .addresses(user.getAddresses() != null ?
                        user.getAddresses().stream()
                                .map(this::toAddressDto)
                                .collect(Collectors.toList()) :
                        new ArrayList<>())
                .build();
    }

    public AddressDto toAddressDto(Address address) {
        return AddressDto.builder()
                .id(String.valueOf(address.getId()))
                .type(address.getType())
                .street(address.getStreet())
                .city(address.getCity())
                .state(address.getState())
                .zipCode(address.getZipCode())
                .isDefault(address.getIsDefault())
                .build();
    }

    public ProductDto toProductDto(Product product) {
        return ProductDto.builder()
                .id(String.valueOf(product.getId()))
                .name(product.getName())
                .description(product.getDescription())
                .price(product.getPrice())
                .originalPrice(product.getOriginalPrice())
                .discount(product.getDiscount())
                .image(product.getImage())
                .category(product.getCategory().getName())
                .unit(product.getUnit())
                .inStock(product.getInStock())
                .rating(product.getRating())
                .reviewCount(product.getReviewCount())
                .tags(product.getTags() != null ?
                        product.getTags().stream()
                                .map(ProductTag::getName)
                                .collect(Collectors.toList()) :
                        new ArrayList<>())
                .build();
    }

    public CategoryDto toCategoryDto(Category category) {
        return CategoryDto.builder()
                .id(String.valueOf(category.getId()))
                .name(category.getName())
                .image(category.getImage())
                .icon(category.getIcon())
                .color(category.getColor())
                .isActive(category.getIsActive())
                .sortOrder(category.getSortOrder())
                .build();
    }

    public BannerDto toBannerDto(Banner banner) {
        return BannerDto.builder()
                .id(String.valueOf(banner.getId()))
                .title(banner.getTitle())
                .subtitle(banner.getSubtitle())
                .image(banner.getImage())
                .backgroundColor(banner.getBackgroundColor())
                .textColor(banner.getTextColor())
                .actionText(banner.getActionText())
                .actionUrl(banner.getActionUrl())
                .isActive(banner.getIsActive())
                .sortOrder(banner.getSortOrder())
                .createdAt(banner.getCreatedAt().toString())
                .build();
    }

    public OrderDto toOrderDto(Order order) {
        return OrderDto.builder()
                .id(String.valueOf(order.getId()))
                .orderNumber(order.getOrderNumber())
                .total(order.getTotal())
                .subtotal(order.getSubtotal())
                .deliveryFee(order.getDeliveryFee())
                .discount(order.getDiscount())
                .discountCode(order.getDiscountCode())
                .status(order.getStatus())
                .deliveryAddress(toAddressDto(order.getDeliveryAddress()))
                .orderDate(order.getCreatedAt())
                .estimatedDelivery(order.getEstimatedDelivery())
                .items(order.getItems().stream()
                        .map(this::toOrderItemDto)
                        .collect(Collectors.toList()))
                .build();
    }

    public OrderItemDto toOrderItemDto(OrderItem orderItem) {
        return OrderItemDto.builder()
                .product(toProductDto(orderItem.getProduct()))
                .quantity(orderItem.getQuantity())
                .unitPrice(orderItem.getUnitPrice())
                .totalPrice(orderItem.getTotalPrice())
                .build();
    }

    public List<ProductDto> toProductDtoList(List<Product> products) {
        return products.stream()
                .map(this::toProductDto)
                .collect(Collectors.toList());
    }

    public List<CategoryDto> toCategoryDtoList(List<Category> categories) {
        return categories.stream()
                .map(this::toCategoryDto)
                .collect(Collectors.toList());
    }

    public List<BannerDto> toBannerDtoList(List<Banner> banners) {
        return banners.stream()
                .map(this::toBannerDto)
                .collect(Collectors.toList());
    }

    public List<OrderDto> toOrderDtoList(List<Order> orders) {
        return orders.stream()
                .map(this::toOrderDto)
                .collect(Collectors.toList());
    }

    public DiscountCodeDto toDiscountCodeDto(DiscountCode discountCode) {
        List<String> applicableCategories = new ArrayList<>();
        List<String> applicableProducts = new ArrayList<>();

        try {
            if (discountCode.getApplicableCategories() != null) {
                List<Long> categoryIds = objectMapper.readValue(
                        discountCode.getApplicableCategories(), new TypeReference<List<Long>>() {});
                applicableCategories = categoryIds.stream()
                        .map(String::valueOf)
                        .collect(Collectors.toList());
            }

            if (discountCode.getApplicableProducts() != null) {
                List<Long> productIds = objectMapper.readValue(
                        discountCode.getApplicableProducts(), new TypeReference<List<Long>>() {});
                applicableProducts = productIds.stream()
                        .map(String::valueOf)
                        .collect(Collectors.toList());
            }
        } catch (JsonProcessingException e) {
            log.error("Error parsing applicable categories/products for discount code: {}", discountCode.getCode(), e);
        }

        return DiscountCodeDto.builder()
                .id(String.valueOf(discountCode.getId()))
                .code(discountCode.getCode())
                .name(discountCode.getName())
                .description(discountCode.getDescription())
                .type(discountCode.getType())
                .value(discountCode.getValue())
                .minimumOrderAmount(discountCode.getMinimumOrderAmount())
                .maximumDiscountAmount(discountCode.getMaximumDiscountAmount())
                .usageLimit(discountCode.getUsageLimit())
                .usageLimitPerUser(discountCode.getUsageLimitPerUser())
                .usedCount(discountCode.getUsedCount())
                .validFrom(discountCode.getValidFrom())
                .validUntil(discountCode.getValidUntil())
                .isActive(discountCode.getIsActive())
                .isFirstOrderOnly(discountCode.getIsFirstOrderOnly())
                .applicableCategories(applicableCategories)
                .applicableProducts(applicableProducts)
                .createdAt(discountCode.getCreatedAt())
                .isValid(discountCode.isValid())
                .isExpired(discountCode.isExpired())
                .build();
    }

    public List<DiscountCodeDto> toDiscountCodeDtoList(List<DiscountCode> discountCodes) {
        return discountCodes.stream()
                .map(this::toDiscountCodeDto)
                .collect(Collectors.toList());
    }
}
