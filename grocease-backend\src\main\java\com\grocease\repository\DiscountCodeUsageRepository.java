package com.grocease.repository;

import com.grocease.entity.DiscountCodeUsage;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface DiscountCodeUsageRepository extends JpaRepository<DiscountCodeUsage, Long> {

    @Query("SELECT COUNT(u) FROM DiscountCodeUsage u WHERE u.discountCode.id = :discountCodeId AND u.user.id = :userId")
    Long countByDiscountCodeIdAndUserId(@Param("discountCodeId") Long discountCodeId, @Param("userId") Long userId);

    @Query("SELECT u FROM DiscountCodeUsage u WHERE u.user.id = :userId ORDER BY u.createdAt DESC")
    Page<DiscountCodeUsage> findByUserIdOrderByCreatedAtDesc(@Param("userId") Long userId, Pageable pageable);

    @Query("SELECT u FROM DiscountCodeUsage u WHERE u.discountCode.id = :discountCodeId ORDER BY u.createdAt DESC")
    Page<DiscountCodeUsage> findByDiscountCodeIdOrderByCreatedAtDesc(@Param("discountCodeId") Long discountCodeId, Pageable pageable);

    @Query("SELECT SUM(u.discountAmount) FROM DiscountCodeUsage u WHERE u.discountCode.id = :discountCodeId")
    BigDecimal getTotalDiscountAmountByCode(@Param("discountCodeId") Long discountCodeId);

    @Query("SELECT SUM(u.discountAmount) FROM DiscountCodeUsage u WHERE u.createdAt >= :startDate AND u.createdAt <= :endDate")
    BigDecimal getTotalDiscountAmountInPeriod(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    @Query("SELECT COUNT(u) FROM DiscountCodeUsage u WHERE u.user.id = :userId")
    Long countTotalUsagesByUser(@Param("userId") Long userId);

    @Query("SELECT u FROM DiscountCodeUsage u WHERE u.discountCode.code = :code ORDER BY u.createdAt DESC")
    List<DiscountCodeUsage> findByDiscountCodeCode(@Param("code") String code);
}
