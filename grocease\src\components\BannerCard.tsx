import React from 'react';
import { TouchableOpacity, Text, View, ImageBackground, Dimensions } from 'react-native';
import { Banner } from '../types';

interface BannerCardProps {
  banner: Banner;
  onPress?: (banner: Banner) => void;
}

const { width } = Dimensions.get('window');
const bannerWidth = width - 32; // Account for horizontal padding

const BannerCard: React.FC<BannerCardProps> = ({ banner, onPress }) => {
  return (
    <TouchableOpacity
      className="rounded-2xl overflow-hidden shadow-sm mr-4"
      style={{ width: bannerWidth }}
      onPress={() => onPress?.(banner)}
      activeOpacity={0.9}
    >
      <ImageBackground
        source={{ uri: banner.image }}
        className="h-40 justify-end"
        imageStyle={{ borderRadius: 16 }}
      >
        <View 
          className="absolute inset-0 rounded-2xl"
          style={{ backgroundColor: banner.backgroundColor + '80' }}
        />
        <View className="p-6">
          <Text 
            className="text-2xl font-bold mb-2"
            style={{ color: banner.textColor }}
          >
            {banner.title}
          </Text>
          <Text 
            className="text-base mb-4"
            style={{ color: banner.textColor }}
          >
            {banner.subtitle}
          </Text>
          {banner.actionText && (
            <View className="self-start">
              <Text 
                className="text-sm font-semibold px-4 py-2 bg-white rounded-full"
                style={{ color: banner.backgroundColor }}
              >
                {banner.actionText}
              </Text>
            </View>
          )}
        </View>
      </ImageBackground>
    </TouchableOpacity>
  );
};

export default BannerCard;
