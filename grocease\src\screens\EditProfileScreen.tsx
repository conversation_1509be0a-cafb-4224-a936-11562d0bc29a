import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  TextInput,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../hooks/useAuth';
import { useTheme } from '../hooks/useTheme';
import { useAlert } from '../hooks/useCustomAlert';
import LoadingSpinner from '../components/LoadingSpinner';
import Button from '../components/Button';

const EditProfileScreen = () => {
  const navigation = useNavigation();
  const { user, updateProfile } = useAuth();
  const { isDark } = useTheme();
  const { success, error, confirm, destructive } = useAlert();
  
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: user?.name || '',
    email: user?.email || '',
    phone: user?.phone || '',
    avatar: user?.avatar || ''
  });

  const [errors, setErrors] = useState<{[key: string]: string}>({});

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    } else if (!/^\+?[\d\s\-\(\)]+$/.test(formData.phone)) {
      newErrors.phone = 'Please enter a valid phone number';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    try {
      await updateProfile(formData);
      success(
        'Success',
        'Profile updated successfully!',
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    } catch (updateError) {
      error(
        'Error',
        'Failed to update profile. Please try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleAvatarPress = () => {
    confirm(
      'Change Avatar',
      'Choose how you want to update your profile picture',
      () => console.log('Camera selected'),
      () => console.log('Gallery selected'),
      'Camera',
      'Gallery'
    );
  };

  const renderInput = (
    label: string,
    value: string,
    onChangeText: (text: string) => void,
    placeholder: string,
    keyboardType: 'default' | 'email-address' | 'phone-pad' = 'default',
    error?: string
  ) => (
    <View className="mb-4">
      <Text className={`text-sm font-medium mb-2 ${isDark ? 'text-neutral-200' : 'text-neutral-700'}`}>
        {label}
      </Text>
      <TextInput
        value={value}
        onChangeText={onChangeText}
        placeholder={placeholder}
        placeholderTextColor={isDark ? '#a3a3a3' : '#9ca3af'}
        keyboardType={keyboardType}
        className={`border rounded-xl px-4 py-3 text-base ${
          error 
            ? 'border-error-500 bg-error-50' 
            : isDark 
              ? 'border-neutral-600 bg-neutral-800 text-neutral-100' 
              : 'border-neutral-300 bg-white text-neutral-900'
        }`}
      />
      {error && (
        <Text className="text-error-500 text-sm mt-1">{error}</Text>
      )}
    </View>
  );

  if (isLoading) {
    return <LoadingSpinner message="Updating profile..." fullScreen />;
  }

  return (
    <SafeAreaView className={`flex-1 ${isDark ? 'bg-neutral-900' : 'bg-neutral-50'}`}>
      <KeyboardAvoidingView 
        className="flex-1" 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* Header */}
        <View className={`px-4 py-4 border-b ${isDark ? 'bg-neutral-800 border-neutral-700' : 'bg-white border-neutral-200'}`}>
          <View className="flex-row items-center justify-between">
            <TouchableOpacity
              onPress={() => navigation.goBack()}
              className="w-10 h-10 items-center justify-center"
            >
              <Ionicons 
                name="arrow-back" 
                size={24} 
                color={isDark ? '#f5f5f5' : '#1e293b'} 
              />
            </TouchableOpacity>
            <Text className={`text-lg font-bold ${isDark ? 'text-neutral-100' : 'text-neutral-800'}`}>
              Edit Profile
            </Text>
            <View className="w-10" />
          </View>
        </View>

        <ScrollView className="flex-1 px-4 py-6" showsVerticalScrollIndicator={false}>
          {/* Avatar Section */}
          <View className="items-center mb-8">
            <TouchableOpacity onPress={handleAvatarPress} className="relative">
              <Image
                source={{
                  uri: formData.avatar || 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400'
                }}
                className="w-24 h-24 rounded-full"
              />
              <View className="absolute bottom-0 right-0 w-8 h-8 bg-primary-500 rounded-full items-center justify-center border-2 border-white">
                <Ionicons name="camera" size={16} color="white" />
              </View>
            </TouchableOpacity>
            <Text className={`text-sm mt-2 ${isDark ? 'text-neutral-400' : 'text-neutral-600'}`}>
              Tap to change photo
            </Text>
          </View>

          {/* Form Fields */}
          <View className={`rounded-2xl p-6 mb-6 ${isDark ? 'bg-neutral-800' : 'bg-white'}`}>
            {renderInput(
              'Full Name',
              formData.name,
              (text) => setFormData(prev => ({ ...prev, name: text })),
              'Enter your full name',
              'default',
              errors.name
            )}

            {renderInput(
              'Email Address',
              formData.email,
              (text) => setFormData(prev => ({ ...prev, email: text })),
              'Enter your email address',
              'email-address',
              errors.email
            )}

            {renderInput(
              'Phone Number',
              formData.phone,
              (text) => setFormData(prev => ({ ...prev, phone: text })),
              'Enter your phone number',
              'phone-pad',
              errors.phone
            )}
          </View>

          {/* Save Button */}
          <Button
            title="Save Changes"
            onPress={handleSave}
            disabled={isLoading}
            className="mb-6"
          />

          {/* Delete Account */}
          <TouchableOpacity
            className={`rounded-xl p-4 border ${isDark ? 'border-error-800 bg-error-900/20' : 'border-error-200 bg-error-50'}`}
            onPress={() => {
              destructive(
                'Delete Account',
                'Are you sure you want to delete your account? This action cannot be undone.',
                () => console.log('Delete account'),
                undefined,
                'Delete',
                'Cancel'
              );
            }}
          >
            <View className="flex-row items-center justify-center">
              <Ionicons name="trash-outline" size={20} color="#ef4444" />
              <Text className="text-error-500 font-medium ml-2">
                Delete Account
              </Text>
            </View>
          </TouchableOpacity>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default EditProfileScreen;
