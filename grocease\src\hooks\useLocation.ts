import { useState, useEffect } from 'react';
import * as Location from 'expo-location';

interface LocationData {
  latitude: number;
  longitude: number;
  address: string;
  city: string;
  state: string;
}

interface UseLocationReturn {
  location: LocationData | null;
  loading: boolean;
  error: string | null;
  requestLocation: () => Promise<void>;
}

export const useLocation = (): UseLocationReturn => {
  const [location, setLocation] = useState<LocationData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const requestLocation = async () => {
    try {
      setLoading(true);
      setError(null);

      // Request permission
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        setError('Permission to access location was denied');
        return;
      }

      // Get current position
      const position = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
      });

      // Reverse geocode to get address
      const reverseGeocode = await Location.reverseGeocodeAsync({
        latitude: position.coords.latitude,
        longitude: position.coords.longitude,
      });

      if (reverseGeocode.length > 0) {
        const address = reverseGeocode[0];
        setLocation({
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          address: `${address.street || ''} ${address.streetNumber || ''}`.trim(),
          city: address.city || 'Unknown City',
          state: address.region || 'Unknown State',
        });
      } else {
        // Fallback if reverse geocoding fails
        setLocation({
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          address: 'Current Location',
          city: 'San Francisco',
          state: 'CA',
        });
      }
    } catch (err) {
      setError('Failed to get location');
      // Set a default location for demo purposes
      setLocation({
        latitude: 37.7749,
        longitude: -122.4194,
        address: '123 Main Street',
        city: 'San Francisco',
        state: 'CA',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    requestLocation();
  }, []);

  return {
    location,
    loading,
    error,
    requestLocation,
  };
};
