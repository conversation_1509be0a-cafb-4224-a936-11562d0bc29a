import { useState, useCallback } from 'react';
import { ApiResponse } from '../types';
import { AppError, parseApiError, createErrorResponse } from '../utils/errorHandler';

export interface UseApiCallState<T> {
  data: T | null;
  isLoading: boolean;
  error: AppError | null;
  isSuccess: boolean;
  isError: boolean;
}

export interface UseApiCallReturn<T> extends UseApiCallState<T> {
  execute: (...args: any[]) => Promise<T | null>;
  reset: () => void;
  clearError: () => void;
}

export function useApiCall<T>(
  apiFunction: (...args: any[]) => Promise<ApiResponse<T>>
): UseApiCallReturn<T> {
  const [state, setState] = useState<UseApiCallState<T>>({
    data: null,
    isLoading: false,
    error: null,
    isSuccess: false,
    isError: false,
  });

  const execute = useCallback(
    async (...args: any[]): Promise<T | null> => {
      setState(prev => ({
        ...prev,
        isLoading: true,
        error: null,
        isSuccess: false,
        isError: false,
      }));

      try {
        const response = await apiFunction(...args);
        
        if (response.success) {
          setState(prev => ({
            ...prev,
            data: response.data,
            isLoading: false,
            isSuccess: true,
            isError: false,
          }));
          return response.data;
        } else {
          const error = parseApiError(new Error(response.message || response.error));
          setState(prev => ({
            ...prev,
            isLoading: false,
            error,
            isSuccess: false,
            isError: true,
          }));
          return null;
        }
      } catch (error) {
        const appError = parseApiError(error);
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: appError,
          isSuccess: false,
          isError: true,
        }));
        return null;
      }
    },
    [apiFunction]
  );

  const reset = useCallback(() => {
    setState({
      data: null,
      isLoading: false,
      error: null,
      isSuccess: false,
      isError: false,
    });
  }, []);

  const clearError = useCallback(() => {
    setState(prev => ({
      ...prev,
      error: null,
      isError: false,
    }));
  }, []);

  return {
    ...state,
    execute,
    reset,
    clearError,
  };
}

// Specialized hook for paginated data
export interface UsePaginatedApiCallState<T> extends UseApiCallState<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  } | null;
  hasMore: boolean;
}

export interface UsePaginatedApiCallReturn<T> extends UsePaginatedApiCallState<T> {
  execute: (page?: number, limit?: number, ...args: any[]) => Promise<T[] | null>;
  loadMore: () => Promise<T[] | null>;
  reset: () => void;
  clearError: () => void;
}

export function usePaginatedApiCall<T>(
  apiFunction: (...args: any[]) => Promise<{ data: T[]; pagination: any }>
): UsePaginatedApiCallReturn<T> {
  const [state, setState] = useState<UsePaginatedApiCallState<T>>({
    data: [],
    isLoading: false,
    error: null,
    isSuccess: false,
    isError: false,
    pagination: null,
    hasMore: false,
  });

  const execute = useCallback(
    async (page: number = 0, limit: number = 10, ...args: any[]): Promise<T[] | null> => {
      setState(prev => ({
        ...prev,
        isLoading: true,
        error: null,
        isSuccess: false,
        isError: false,
      }));

      try {
        const response = await apiFunction(page, limit, ...args);
        
        setState(prev => ({
          ...prev,
          data: response.data,
          pagination: response.pagination,
          hasMore: response.pagination.page < response.pagination.totalPages - 1,
          isLoading: false,
          isSuccess: true,
          isError: false,
        }));
        
        return response.data;
      } catch (error) {
        const appError = parseApiError(error);
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: appError,
          isSuccess: false,
          isError: true,
        }));
        return null;
      }
    },
    [apiFunction]
  );

  const loadMore = useCallback(async (): Promise<T[] | null> => {
    if (!state.pagination || !state.hasMore || state.isLoading) {
      return null;
    }

    const nextPage = state.pagination.page + 1;
    setState(prev => ({ ...prev, isLoading: true }));

    try {
      const response = await apiFunction(nextPage, state.pagination!.limit);
      
      setState(prev => ({
        ...prev,
        data: [...prev.data, ...response.data],
        pagination: response.pagination,
        hasMore: response.pagination.page < response.pagination.totalPages - 1,
        isLoading: false,
      }));
      
      return response.data;
    } catch (error) {
      const appError = parseApiError(error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: appError,
        isError: true,
      }));
      return null;
    }
  }, [apiFunction, state.pagination, state.hasMore, state.isLoading, state.data]);

  const reset = useCallback(() => {
    setState({
      data: [],
      isLoading: false,
      error: null,
      isSuccess: false,
      isError: false,
      pagination: null,
      hasMore: false,
    });
  }, []);

  const clearError = useCallback(() => {
    setState(prev => ({
      ...prev,
      error: null,
      isError: false,
    }));
  }, []);

  return {
    ...state,
    execute,
    loadMore,
    reset,
    clearError,
  };
}
